import re

def comprehensive_bug_fix(input_file, output_file):
    """
    Comprehensive bug fix for all remaining issues:
    1. Fix malformed subsection titles
    2. Fix incomplete hyperref setup
    3. Fix orphaned numbers and incomplete text
    4. Ensure proper paragraph structure
    5. Fix any incomplete sentences or formatting
    """
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Starting comprehensive bug fix: {len(content)} characters")
    
    # 1. Fix incomplete hyperref setup
    content = content.replace(
        'pdftitle={Phase 1 Clinical Trials: Comprehensive Overview}, pdfpagemode=FullScreen,\n\n}',
        'pdftitle={Phase 1 Clinical Trials: Comprehensive Overview}, pdfpagemode=FullScreen\n}'
    )
    
    # 2. Fix malformed subsection titles - these should be proper titles, not descriptions
    subsection_fixes = [
        (r'\\subsection\{Phase 1 trials have distinct objectives that lay the foundation for further drug development\}',
         r'\\subsection{Primary Objectives}'),
        (r'\\subsection\{what dose\(s\) to take into Phase 2 trials \(which assess efficacy in patients\)\. Scenarios include\}',
         r'\\subsection{Dose Selection for Phase 2}'),
        (r'\\subsection\{have shaped regulatory approaches\}',
         r'\\subsection{Historical Cases That Shaped Regulatory Approaches}'),
        (r'\\subsection\{Despite their critical role, Phase 1 trials have inherent limitations that must be acknowledged\}',
         r'\\subsection{Inherent Limitations of Phase 1 Trials}')
    ]
    
    for old_pattern, new_pattern in subsection_fixes:
        content = re.sub(old_pattern, new_pattern, content)
    
    # 3. Remove any remaining orphaned numbers
    content = re.sub(r'\n\s*\d+\s*\n', '\n\n', content)
    
    # 4. Fix broken text that should start new paragraphs after subsections
    # After subsection titles, the next content should start on a new line
    content = re.sub(r'(\\subsection\{[^}]+\})\n\n([A-Z])', r'\1\n\n\2', content)
    
    # 5. Fix specific formatting issues I noticed
    
    # Fix paragraph breaks that got lost
    content = re.sub(r'(\w+)\s*\n\s*([A-Z][a-z]+ [A-Z][a-z]+:)', r'\1\n\n\2', content)
    
    # Fix numbered list items that got broken
    content = re.sub(r'\n\s*2\.\s*\n', '\n\n2. ', content)
    content = re.sub(r'\n\s*3\.\s*\n', '\n\n3. ', content)
    
    # 6. Ensure proper spacing around sections and subsections
    content = re.sub(r'(\\section\{[^}]+\})\n([A-Z])', r'\1\n\n\2', content)
    content = re.sub(r'(\\subsection\{[^}]+\})\n([A-Z])', r'\1\n\n\2', content)
    
    # 7. Fix specific text flow issues where sentences got broken
    
    # Common patterns where lines got broken mid-sentence
    patterns_to_fix = [
        (r'Phase 1 is to assess safety and tolerability and to establish a safe dosage range for subsequent trials\.\s*\n\s*Additionally',
         'Phase 1 is to assess safety and tolerability and to establish a safe dosage range for subsequent trials. Additionally'),
        (r'metabolism, excretion\) and often pharmacodynamic \(PD\) information, guiding dose selection for later phases\.\s*\n\s*These',
         'metabolism, excretion) and often pharmacodynamic (PD) information, guiding dose selection for later phases. These'),
        (r'who have the relevant illness\s*\n\s*\. By using patients',
         'who have the relevant illness. By using patients'),
    ]
    
    for old_pattern, new_text in patterns_to_fix:
        content = re.sub(old_pattern, new_text, content)
    
    # 8. Clean up multiple consecutive newlines but preserve paragraph breaks
    content = re.sub(r'\n\n\n+', '\n\n', content)
    
    # 9. Fix any remaining spacing issues
    content = re.sub(r' +', ' ', content)  # Multiple spaces to single space
    
    print(f"Bug-fixed document: {len(content)} characters")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Document saved to {output_file}")
    return len(content)

if __name__ == "__main__":
    input_file = "phase1_comprehensive_clean.tex"
    output_file = "phase1_comprehensive_perfect.tex"
    
    char_count = comprehensive_bug_fix(input_file, output_file)
    print(f"\n✅ Comprehensive bug fix completed!")
    print(f"📄 Output: {output_file}")
    print(f"📊 Character count: {char_count:,}")
    print(f"\n🔧 Issues Fixed:")
    print(f"  ✅ Malformed subsection titles")
    print(f"  ✅ Incomplete hyperref setup")
    print(f"  ✅ Orphaned numbers and text")
    print(f"  ✅ Broken paragraph structure")
    print(f"  ✅ Text flow issues")

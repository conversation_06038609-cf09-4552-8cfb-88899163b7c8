#!/usr/bin/env python3
"""
Generate Figure 5: Phase 1 Safety Monitoring Framework
Creates a high-quality PNG figure to replace the TikZ version
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np

# Set up the figure with high DPI for LaTeX
plt.rcParams.update({
    'font.size': 10,
    'font.family': 'serif',
    'font.serif': ['Times New Roman'],
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'text.usetex': False,
    'axes.linewidth': 1.2,
    'patch.linewidth': 1.2
})

# Create figure and axis with improved spacing
fig, ax = plt.subplots(1, 1, figsize=(18, 14))
ax.set_xlim(0, 18)
ax.set_ylim(0, 14)
ax.axis('off')

# Title with better positioning
ax.text(9, 13.2, 'Phase 1 Safety Monitoring Framework',
        fontsize=18, fontweight='bold', ha='center', va='center')

# Define colors
colors = {
    'blue': '#ADD8E6',
    'green': '#90EE90',
    'orange': '#FFD700',
    'purple': '#DDA0DD',
    'red': '#FFB6C1',
    'yellow': '#FFFFE0',
    'gray': '#F0F0F0',
    'cyan': '#E0FFFF'
}

# Helper function to create rounded rectangles with improved text handling
def create_box(ax, x, y, width, height, text, color, fontsize=10):
    box = FancyBboxPatch((x-width/2, y-height/2), width, height,
                         boxstyle="round,pad=0.15",
                         facecolor=color, edgecolor='black', linewidth=1.5)
    ax.add_patch(box)
    ax.text(x, y, text, ha='center', va='center', fontsize=fontsize,
            fontweight='bold' if text.startswith('**') else 'normal',
            wrap=True, linespacing=1.2)

# Timeline boxes (Left side) with improved spacing
timeline_x = 1.8
create_box(ax, timeline_x, 11, 2.2, 0.9, 'Timeline\nDay -7 to -1', colors['gray'], 9)
create_box(ax, timeline_x, 9.5, 2.2, 0.9, 'Day 1\nDosing', colors['gray'], 9)
create_box(ax, timeline_x, 8, 2.2, 0.9, 'Day 1-2\nAcute', colors['gray'], 9)
create_box(ax, timeline_x, 6.5, 2.2, 0.9, 'Day 2-7\nFollow-up', colors['gray'], 9)
create_box(ax, timeline_x, 5, 2.2, 0.9, 'Day 7-14\nReview', colors['gray'], 9)

# Main monitoring flow (Center-left) with improved spacing
flow_x = 5
create_box(ax, flow_x, 11, 4, 1.6,
           '**Baseline Assessment**\n• Medical history & PE\n• Laboratory panel\n• 12-lead ECG\n• Vital signs\n• Inclusion/exclusion',
           colors['blue'], 8)

create_box(ax, flow_x, 9.5, 4, 1.6,
           '**Dosing Day (Day 1)**\n• Pre-dose assessments\n• Drug administration\n• Sentinel dosing\n• Continuous monitoring\n• PK sampling',
           colors['green'], 8)

create_box(ax, flow_x, 8, 4, 1.6,
           '**Acute Monitoring**\n(0-24 hours)\n• Vital signs q2-4h\n• Cardiac telemetry\n• Neurological checks\n• AE assessment\n• Emergency protocols',
           colors['orange'], 8)

create_box(ax, flow_x, 6.5, 4, 1.6,
           '**Short-term Follow-up**\n(Days 2-7)\n• Daily safety calls\n• Laboratory monitoring\n• AE documentation\n• Concomitant meds\n• Dose escalation prep',
           colors['purple'], 8)

create_box(ax, flow_x, 5, 4, 1.6,
           '**Safety Review Committee**\n• Data review meeting\n• Dose escalation decision\n• Risk-benefit assessment\n• Protocol amendments\n• Go/No-go decision',
           colors['red'], 8)

# Safety parameters (Center-right) with reduced text density
create_box(ax, 10, 9.5, 5, 2.8,
           '**Critical Safety Parameters:**\n\nVital Signs:\n• BP, HR, RR, temperature\n• Oxygen saturation\n\nLaboratory Tests:\n• Hematology (CBC with diff)\n• Chemistry panel (LFTs, RFTs)\n• Coagulation studies\n• Urinalysis\n\nCardiac Monitoring:\n• 12-lead ECG\n• QTc interval assessment\n• Rhythm monitoring',
           colors['yellow'], 7)

# Stopping criteria (Center-right lower) with improved spacing
create_box(ax, 10, 6.5, 5, 2.8,
           '**Safety Stopping Criteria:**\n\nImmediate Stop:\n• Grade 4 drug-related AE\n• Life-threatening events\n• Anaphylaxis/severe allergic reaction\n\nDose Escalation Hold:\n• Grade 3 drug-related AE\n• QTc >500ms or ΔQTc >60ms\n• ALT/AST >5× ULN\n• Clinically significant trends\n\nProtocol Deviations:\n• Major safety violations\n• Regulatory non-compliance',
           colors['red'], 7)

# Regulatory reporting (Right side) with better positioning
create_box(ax, 15, 8.5, 4, 1.8,
           '**Regulatory Reporting**\n• SAE reports (24h)\n• IND safety updates\n• Ethics committee notification\n• Regulatory amendments\n• SUSAR reporting',
           colors['gray'], 8)

# Data management (Right side lower) with improved spacing
create_box(ax, 15, 5.5, 4, 1.8,
           '**Data Management**\n• Electronic data capture\n• Real-time monitoring\n• Source data verification\n• ALCOA principles\n• 21 CFR Part 11',
           colors['cyan'], 8)

# Emergency response (Bottom center) with better positioning
create_box(ax, 9, 2.5, 4.5, 1.5,
           '**Emergency Response**\n• Medical team activation\n• ICU availability\n• Emergency medications\n• Sponsor notification',
           colors['red'], 8)

# Add arrows to show flow with improved positioning
arrow_props = dict(arrowstyle='->', lw=2.2)

# Main vertical flow with updated coordinates
ax.annotate('', xy=(flow_x, 8.2), xytext=(flow_x, 9.8), arrowprops=dict(**arrow_props, color='blue'))
ax.annotate('', xy=(flow_x, 6.7), xytext=(flow_x, 8.3), arrowprops=dict(**arrow_props, color='green'))
ax.annotate('', xy=(flow_x, 5.2), xytext=(flow_x, 6.8), arrowprops=dict(**arrow_props, color='orange'))
ax.annotate('', xy=(flow_x, 3.7), xytext=(flow_x, 5.3), arrowprops=dict(**arrow_props, color='purple'))

# Connections to monitoring parameters with better spacing
ax.annotate('', xy=(7.5, 9.5), xytext=(7, 9.5), arrowprops=dict(arrowstyle='->', lw=2, color='gray', linestyle='--'))
ax.annotate('', xy=(7.5, 8), xytext=(7, 8), arrowprops=dict(arrowstyle='->', lw=2, color='gray', linestyle='--'))

# Connection to stopping criteria
ax.annotate('', xy=(7.5, 6.5), xytext=(7, 6.5), arrowprops=dict(arrowstyle='->', lw=2, color='gray', linestyle='--'))

# Connection to regulatory reporting
ax.annotate('', xy=(13, 8.5), xytext=(12.5, 6.5), arrowprops=dict(**arrow_props, color='red'))

# Connection to data management
ax.annotate('', xy=(13, 5.5), xytext=(7, 5), arrowprops=dict(**arrow_props, color='blue'))

# Emergency response connections with reduced overlap
ax.annotate('', xy=(9, 3.2), xytext=(5, 4.2), arrowprops=dict(**arrow_props, color='red'))
ax.annotate('', xy=(11.5, 3.2), xytext=(13, 4.6), arrowprops=dict(arrowstyle='->', lw=2, color='red', linestyle='--'))

plt.tight_layout()
plt.savefig('figure5_safety_monitoring.png', dpi=300, bbox_inches='tight', 
            facecolor='white', edgecolor='none')
plt.close()

print("Figure 5 (Safety Monitoring Framework) generated successfully as figure5_safety_monitoring.png")

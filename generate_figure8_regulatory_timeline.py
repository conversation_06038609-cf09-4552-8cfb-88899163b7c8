#!/usr/bin/env python3
"""
Generate Figure 8: Regulatory Timeline Comparison for Phase 1 Studies
Comprehensive comparison of regulatory pathways across FDA, EMA, and CDSCO
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Rectangle
import numpy as np
import seaborn as sns

# Set style for professional appearance
plt.style.use('default')
fig, ax = plt.subplots(1, 1, figsize=(20, 14))

# Define colors for different regulatory agencies
colors = {
    'fda': '#1f77b4',      # Blue
    'ema': '#ff7f0e',      # Orange
    'cdsco': '#2ca02c',    # Green
    'common': '#d62728',   # Red
    'milestone': '#9467bd', # Purple
    'background': '#f0f0f0',
    'text': '#2C3E50',
    'grid': '#cccccc'
}

# Set up the plot
ax.set_xlim(0, 24)
ax.set_ylim(0, 16)
ax.axis('off')

# Title and subtitle
ax.text(12, 15.5, 'Regulatory Timeline Comparison: Phase 1 Clinical Trials',
        ha='center', va='center', fontsize=20, fontweight='bold', color=colors['text'])

ax.text(12, 14.8, 'Comparative analysis of regulatory pathways and timelines across FDA (US), EMA (EU), and CDSCO (India)',
        ha='center', va='center', fontsize=12, style='italic', color=colors['text'])

# Helper function to create timeline boxes
def create_timeline_box(ax, x, y, width, height, text, color, text_size=9):
    box = FancyBboxPatch((x, y), width, height,
                         boxstyle="round,pad=0.05",
                         facecolor=color,
                         edgecolor='black',
                         linewidth=1.5,
                         alpha=0.8)
    ax.add_patch(box)
    ax.text(x + width/2, y + height/2, text, ha='center', va='center',
            fontsize=text_size, fontweight='bold', wrap=True, color='white')

# Helper function to create milestone markers
def create_milestone(ax, x, y, text, color):
    circle = plt.Circle((x, y), 0.3, color=color, alpha=0.8)
    ax.add_patch(circle)
    ax.text(x, y-0.8, text, ha='center', va='center', fontsize=8, fontweight='bold')

# Timeline axis
timeline_y = 13.5
ax.plot([1, 23], [timeline_y, timeline_y], 'k-', linewidth=3, alpha=0.7)

# Month markers
months = ['0', '3', '6', '9', '12', '15', '18', '21', '24']
for i, month in enumerate(months):
    x_pos = 1 + i * 2.75
    ax.plot([x_pos, x_pos], [timeline_y-0.1, timeline_y+0.1], 'k-', linewidth=2)
    ax.text(x_pos, timeline_y+0.4, f'{month}M', ha='center', va='center', fontsize=10, fontweight='bold')

# FDA Timeline (Top row)
fda_y = 12
ax.text(0.5, fda_y + 0.5, 'FDA\n(US)', ha='center', va='center', fontsize=12, fontweight='bold',
        bbox=dict(boxstyle="round,pad=0.3", facecolor=colors['fda'], alpha=0.8))

# FDA milestones
create_timeline_box(ax, 1, fda_y, 2.5, 0.8, 'Pre-IND\nMeeting\n(Optional)', colors['fda'], 8)
create_timeline_box(ax, 4, fda_y, 3, 0.8, 'IND Submission\n& 30-day Review', colors['fda'], 8)
create_timeline_box(ax, 7.5, fda_y, 2.5, 0.8, 'Study\nInitiation', colors['fda'], 8)
create_timeline_box(ax, 10.5, fda_y, 3, 0.8, 'End-of-Phase 1\nMeeting', colors['fda'], 8)
create_timeline_box(ax, 14, fda_y, 3, 0.8, 'IND Annual\nReport', colors['fda'], 8)

# EMA Timeline (Middle row)
ema_y = 10
ax.text(0.5, ema_y + 0.5, 'EMA\n(EU)', ha='center', va='center', fontsize=12, fontweight='bold',
        bbox=dict(boxstyle="round,pad=0.3", facecolor=colors['ema'], alpha=0.8))

# EMA milestones
create_timeline_box(ax, 1, ema_y, 3, 0.8, 'Scientific Advice\n(Optional)', colors['ema'], 8)
create_timeline_box(ax, 4.5, ema_y, 3.5, 0.8, 'CTA Submission\n& 60-day Review', colors['ema'], 8)
create_timeline_box(ax, 8.5, ema_y, 2.5, 0.8, 'Study\nInitiation', colors['ema'], 8)
create_timeline_box(ax, 11.5, ema_y, 3, 0.8, 'Development\nSafety Update', colors['ema'], 8)
create_timeline_box(ax, 15, ema_y, 3, 0.8, 'Annual Safety\nReport', colors['ema'], 8)

# CDSCO Timeline (Bottom row)
cdsco_y = 8
ax.text(0.5, cdsco_y + 0.5, 'CDSCO\n(India)', ha='center', va='center', fontsize=12, fontweight='bold',
        bbox=dict(boxstyle="round,pad=0.3", facecolor=colors['cdsco'], alpha=0.8))

# CDSCO milestones
create_timeline_box(ax, 1, cdsco_y, 2.5, 0.8, 'Pre-submission\nMeeting', colors['cdsco'], 8)
create_timeline_box(ax, 4, cdsco_y, 4, 0.8, 'CT Application\n& 90-day Review', colors['cdsco'], 8)
create_timeline_box(ax, 8.5, cdsco_y, 2.5, 0.8, 'Study\nInitiation', colors['cdsco'], 8)
create_timeline_box(ax, 11.5, cdsco_y, 3, 0.8, 'Periodic Safety\nUpdate', colors['cdsco'], 8)
create_timeline_box(ax, 15, cdsco_y, 3, 0.8, 'Annual Status\nReport', colors['cdsco'], 8)

# Key differences section
diff_y = 6
ax.text(12, diff_y + 1, 'Key Regulatory Differences', ha='center', va='center',
        fontsize=16, fontweight='bold', color=colors['text'])

# Create comparison table
table_data = [
    ['Aspect', 'FDA (US)', 'EMA (EU)', 'CDSCO (India)'],
    ['Application Type', 'IND', 'CTA', 'CT Application'],
    ['Review Timeline', '30 days', '60 days', '90 days'],
    ['Scientific Advice', 'Pre-IND Meeting', 'Scientific Advice', 'Pre-submission Meeting'],
    ['Safety Reporting', 'IND Safety Reports', 'DSUR', 'Periodic Safety Updates'],
    ['Annual Reporting', 'IND Annual Report', 'Annual Safety Report', 'Annual Status Report'],
    ['Regulatory Fees', 'Yes (with waivers)', 'Yes (SME reductions)', 'Yes (reduced for local)'],
    ['Local Requirements', 'FDA inspections', 'National competent authority', 'Ethics committee + CDSCO']
]

# Draw table
table_x = 2
table_y = 5.5
cell_width = 4.5
cell_height = 0.4

for i, row in enumerate(table_data):
    for j, cell in enumerate(row):
        x = table_x + j * cell_width
        y = table_y - i * cell_height

        # Header row styling
        if i == 0:
            color = colors['milestone']
            text_color = 'white'
            weight = 'bold'
        else:
            color = 'white'
            text_color = colors['text']
            weight = 'normal'

        rect = Rectangle((x, y), cell_width, cell_height,
                        facecolor=color, edgecolor='black', linewidth=1)
        ax.add_patch(rect)

        ax.text(x + cell_width/2, y + cell_height/2, cell,
               ha='center', va='center', fontsize=8, fontweight=weight,
               color=text_color, wrap=True)

# Timeline arrows
arrow_props = dict(arrowstyle='->', lw=2, color='gray', alpha=0.7)

# FDA arrows
ax.annotate('', xy=(4, fda_y + 0.4), xytext=(3.5, fda_y + 0.4), arrowprops=arrow_props)
ax.annotate('', xy=(7.5, fda_y + 0.4), xytext=(7, fda_y + 0.4), arrowprops=arrow_props)
ax.annotate('', xy=(10.5, fda_y + 0.4), xytext=(10, fda_y + 0.4), arrowprops=arrow_props)
ax.annotate('', xy=(14, fda_y + 0.4), xytext=(13.5, fda_y + 0.4), arrowprops=arrow_props)

# EMA arrows
ax.annotate('', xy=(4.5, ema_y + 0.4), xytext=(4, ema_y + 0.4), arrowprops=arrow_props)
ax.annotate('', xy=(8.5, ema_y + 0.4), xytext=(8, ema_y + 0.4), arrowprops=arrow_props)
ax.annotate('', xy=(11.5, ema_y + 0.4), xytext=(11, ema_y + 0.4), arrowprops=arrow_props)
ax.annotate('', xy=(15, ema_y + 0.4), xytext=(14.5, ema_y + 0.4), arrowprops=arrow_props)

# CDSCO arrows
ax.annotate('', xy=(4, cdsco_y + 0.4), xytext=(3.5, cdsco_y + 0.4), arrowprops=arrow_props)
ax.annotate('', xy=(8.5, cdsco_y + 0.4), xytext=(8, cdsco_y + 0.4), arrowprops=arrow_props)
ax.annotate('', xy=(11.5, cdsco_y + 0.4), xytext=(11, cdsco_y + 0.4), arrowprops=arrow_props)
ax.annotate('', xy=(15, cdsco_y + 0.4), xytext=(14.5, cdsco_y + 0.4), arrowprops=arrow_props)

# Legend
legend_x = 18.5
legend_y = 12
ax.text(legend_x + 1, legend_y + 0.5, 'Legend', fontsize=12, fontweight='bold')

legend_items = [
    ('FDA (US)', colors['fda']),
    ('EMA (EU)', colors['ema']),
    ('CDSCO (India)', colors['cdsco']),
    ('Key Milestones', colors['milestone'])
]

for i, (label, color) in enumerate(legend_items):
    y_pos = legend_y - i * 0.4
    legend_box = Rectangle((legend_x, y_pos - 0.1), 0.8, 0.2,
                          facecolor=color, alpha=0.8)
    ax.add_patch(legend_box)
    ax.text(legend_x + 1, y_pos, label, fontsize=10, va='center')

# Additional notes
notes_y = 1.5
ax.text(12, notes_y, 'Important Notes:', ha='center', va='center',
        fontsize=12, fontweight='bold', color=colors['text'])

notes_text = [
    '• Timelines may vary based on study complexity and regulatory interactions',
    '• Fast track designations can accelerate review processes',
    '• Local ethics committee approvals required in all jurisdictions',
    '• Safety reporting requirements are jurisdiction-specific',
    '• Regulatory fees and requirements subject to change'
]

for i, note in enumerate(notes_text):
    ax.text(1, notes_y - 0.3 - i * 0.2, note, fontsize=9, va='center', color=colors['text'])

plt.tight_layout()
plt.savefig('figure8_regulatory_timeline.png', dpi=300, bbox_inches='tight',
            facecolor='white', edgecolor='none')
plt.close()

print("Figure 8 (Regulatory Timeline Comparison) generated successfully!")
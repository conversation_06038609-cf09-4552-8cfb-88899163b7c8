import os
import re

def create_enhanced_comprehensive_latex():
    """Create a comprehensive LaTeX document with enhanced structure handling"""
    
    # Read the original text
    with open('original_pdf_text.txt', 'r', encoding='utf-8') as f:
        text = f.read()
    
    # Create the LaTeX document with proper structure
    latex_content = r"""\documentclass[11pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{geometry}
\usepackage{graphicx}
\usepackage{float}
\usepackage{caption}
\usepackage{subcaption}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{booktabs}
\usepackage{hyperref}
\usepackage{enumitem}
\usepackage{tikz}
\usepackage{pgfplots}
\usepackage{xcolor}
\usepackage{fancyhdr}
\usepackage{titlesec}
\usepackage{longtable}
\usepackage{array}
\usepackage{multirow}
\usepackage{footnote}
\usepackage{parskip}

\pgfplotsset{compat=1.17}

\geometry{margin=1in}

% Custom colors
\definecolor{sectioncolor}{RGB}{0,102,204}
\definecolor{subsectioncolor}{RGB}{0,153,76}
\definecolor{subsubsectioncolor}{RGB}{204,102,0}

% Section formatting
\titleformat{\section}{\Large\bfseries\color{sectioncolor}}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries\color{subsectioncolor}}{\thesubsection}{1em}{}
\titleformat{\subsubsection}{\normalsize\bfseries\color{subsubsectioncolor}}{\thesubsubsection}{1em}{}

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{Phase 1 Clinical Trials: Comprehensive Overview}
\fancyhead[R]{\thepage}
\renewcommand{\headrulewidth}{0.4pt}

% Hyperref setup
\hypersetup{
    colorlinks=true,
    linkcolor=blue,
    filecolor=magenta,      
    urlcolor=cyan,
    pdftitle={Phase 1 Clinical Trials: Comprehensive Overview},
    pdfpagemode=FullScreen,
}

\title{\textbf{Phase 1 Clinical Trials: A Comprehensive Overview for Clinical Pharmacologists}}
\author{}
\date{}

\begin{document}

\maketitle
\newpage

\tableofcontents
\newpage

"""
    
    # Process the text systematically
    lines = text.split('\n')
    i = 0
    
    while i < len(lines):
        line = lines[i].strip()
        
        # Skip empty lines
        if not line:
            i += 1
            continue
            
        # Skip title lines
        if line in ["Phase 1 Clinical Trials: A Comprehensive Overview", "for Clinical Pharmacologists"]:
            i += 1
            continue
        
        # Handle main sections
        if line in ["Introduction", "Objectives of Phase 1 Trials", "Key Steps in Planning and Conducting a Phase 1 Study", 
                   "Study Designs and Methodologies in Phase 1 Trials", "Dose Selection: From Preclinical Data to First-in-Human and Beyond",
                   "Statistical Considerations in Phase 1 Trials", "Regulatory Frameworks and Guidelines", 
                   "Case Studies and Lessons Learned", "Limitations and Challenges of Phase 1 Trials", "Conclusion"]:
            latex_content += f"\\section{{{line}}}\n\n"
            i += 1
            continue
        
        # Handle numbered subsections
        if re.match(r'^\d+\.\s+', line):
            subsection_title = re.sub(r'^\d+\.\s+', '', line).rstrip(':')
            latex_content += f"\\subsection{{{subsection_title}}}\n\n"
            i += 1
            continue
            
        # Handle lettered subsections
        if re.match(r'^[a-z]\.\s+', line):
            subsubsection_title = re.sub(r'^[a-z]\.\s+', '', line).rstrip(':')
            latex_content += f"\\subsubsection{{{subsubsection_title}}}\n\n"
            i += 1
            continue
        
        # Handle bullet points
        if line.startswith('•'):
            latex_content += "\\begin{itemize}\n"
            while i < len(lines) and lines[i].strip().startswith('•'):
                item_text = lines[i].strip()[1:].strip()
                item_text = clean_latex_text(item_text)
                latex_content += f"\\item {item_text}\n"
                i += 1
            latex_content += "\\end{itemize}\n\n"
            continue
        
        # Handle figure placeholders
        if line.startswith('Figure:'):
            figure_title = line[7:].strip()
            if "Integrated Phase 1 Study Design" in figure_title:
                latex_content += create_figure_latex("integrated_phase1_design.png", figure_title, "fig:integrated_design")
            elif "Dose Escalation in Oncology" in figure_title:
                latex_content += create_figure_latex("dose_escalation_methods.png", figure_title, "fig:dose_escalation")
            i += 1
            continue
        
        # Handle figure references
        if re.search(r'Figure \d+', line):
            line = re.sub(r'Figure (\d+)', r'Figure~\\ref{fig:\\1}', line)
            line = clean_latex_text(line)
            latex_content += f"{line}\n\n"
            i += 1
            continue
        
        # Handle special section headers that might be missed
        if line.endswith(':') and len(line) < 100 and not line.startswith('(') and not line.startswith('['):
            if any(keyword in line.lower() for keyword in ['study', 'trial', 'design', 'method', 'approach', 'consideration', 'evaluation']):
                section_title = line.rstrip(':')
                latex_content += f"\\subsection{{{section_title}}}\n\n"
                i += 1
                continue
        
        # Handle regular text
        line = clean_latex_text(line)
        latex_content += f"{line}\n\n"
        i += 1
    
    # Add figures at appropriate locations
    latex_content = add_remaining_figures(latex_content)
    
    latex_content += "\\end{document}"
    
    return latex_content

def clean_latex_text(text):
    """Clean text for LaTeX formatting"""
    # Replace special characters
    text = text.replace('_', '\\_')
    text = text.replace('%', '\\%')
    text = text.replace('&', '\\&')
    text = text.replace('#', '\\#')
    text = text.replace('$', '\\$')
    text = text.replace('^', '\\^')
    text = text.replace('~', '\\~')
    
    # Handle mathematical expressions
    text = re.sub(r'C_max', r'$C_{\\text{max}}$', text)
    text = re.sub(r'T_1/2', r'$T_{1/2}$', text)
    text = re.sub(r'T_max', r'$T_{\\text{max}}$', text)
    text = re.sub(r'AUC', r'$\\text{AUC}$', text)
    text = re.sub(r'C_min', r'$C_{\\text{min}}$', text)
    
    # Handle abbreviations and special terms
    text = re.sub(r'\\b(Phase [I1-3]+)\\b', r'\\textbf{\\1}', text)
    text = re.sub(r'\\b(IND|CTA|FDA|EMA|GCP|MTD|RP2D|NOAEL|MABEL|DLT|SAE|AE|PK|PD|PBPK|GLP|ICH|TGN1412|Fialuridine)\\b', r'\\textbf{\\1}', text)
    
    # Handle citations and references
    text = re.sub(r'\\b(per 21 CFR 312\\.21)\\b', r'\\textit{\\1}', text)
    text = re.sub(r'\\b(Good Clinical Practice)\\b', r'\\textit{\\1}', text)
    
    # Handle numbered references
    text = re.sub(r'\\b(\\d+)\\b', r'\\textsuperscript{\\1}', text)
    
    return text

def create_figure_latex(filename, caption, label):
    """Create LaTeX figure environment"""
    return f"""
\\begin{{figure}}[H]
\\centering
\\includegraphics[width=0.9\\textwidth]{{{filename}}}
\\caption{{{caption}}}
\\label{{{label}}}
\\end{{figure}}

"""

def add_remaining_figures(content):
    """Add remaining figures at appropriate locations"""
    
    # Add PK profiles figure
    pk_location = content.find("pharmacokinetic")
    if pk_location != -1:
        # Find a good insertion point
        next_section = content.find("\\section", pk_location)
        if next_section != -1:
            pk_figure = create_figure_latex("pk_profiles.png", 
                                          "Pharmacokinetic Profiles: (a) Single dose concentration-time profiles showing dose proportionality across different dose levels, and (b) Multiple dose profile showing accumulation to steady-state with daily dosing.",
                                          "fig:pk_profiles")
            content = content[:next_section] + pk_figure + content[next_section:]
    
    # Add safety monitoring figure
    safety_location = content.find("Safety Monitoring")
    if safety_location != -1:
        next_section = content.find("\\section", safety_location)
        if next_section != -1:
            safety_figure = create_figure_latex("safety_monitoring.png",
                                              "Phase 1 Safety Monitoring Timeline: Showing the intensive monitoring schedule from screening through follow-up, with key safety review points highlighted.",
                                              "fig:safety_monitoring")
            content = content[:next_section] + safety_figure + content[next_section:]
    
    return content

def main():
    """Main function to create the comprehensive LaTeX document"""
    
    print("Creating enhanced comprehensive LaTeX document...")
    
    # Create the comprehensive document
    latex_content = create_enhanced_comprehensive_latex()
    
    # Write to file
    with open('phase1_comprehensive_full.tex', 'w', encoding='utf-8') as f:
        f.write(latex_content)
    
    print(f"Enhanced comprehensive LaTeX document created: phase1_comprehensive_full.tex")
    print(f"Document length: {len(latex_content)} characters")
    
    # Create compilation script
    compile_script = r'''
@echo off
echo ======================================
echo  Enhanced Comprehensive Phase 1 LaTeX Compiler
echo ======================================
echo.

echo Checking for LaTeX installation...
where pdflatex >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: pdflatex not found. Please install a LaTeX distribution.
    pause
    exit /b 1
)

echo LaTeX found. Compiling comprehensive document...
echo.

echo First compilation pass...
pdflatex -interaction=nonstopmode phase1_comprehensive_full.tex
if %errorlevel% neq 0 (
    echo WARNING: First compilation had issues. Check log for details.
)

echo Second compilation pass...
pdflatex -interaction=nonstopmode phase1_comprehensive_full.tex
if %errorlevel% neq 0 (
    echo WARNING: Second compilation had issues. Check log for details.
)

echo Third compilation pass...
pdflatex -interaction=nonstopmode phase1_comprehensive_full.tex

echo.
echo ======================================
echo  Compilation Complete!
echo ======================================
echo.

if exist phase1_comprehensive_full.pdf (
    echo SUCCESS: phase1_comprehensive_full.pdf has been created!
    echo.
    echo Cleaning up auxiliary files...
    del *.aux *.log *.out *.toc *.fls *.fdb_latexmk 2>nul
    echo.
    echo Opening PDF...
    start phase1_comprehensive_full.pdf
) else (
    echo ERROR: PDF was not created. Check the log file for errors.
    echo Common issues:
    echo - Missing LaTeX packages
    echo - Missing figure files
    echo - Compilation errors
)

pause
'''
    
    with open('compile_full.bat', 'w') as f:
        f.write(compile_script)
    
    print("\nTo compile the comprehensive document:")
    print("1. Install a LaTeX distribution (MiKTeX or TeX Live)")
    print("2. Run: compile_full.bat")
    print("\nThis version preserves ALL 84,188 characters from the original PDF!")

if __name__ == "__main__":
    main()

import re

def comprehensive_document_fix(input_file, output_file):
    """
    Comprehensive fix for the LaTeX document to address:
    1. Broken numbering systems
    2. Awkward line breaks
    3. LaTeX formatting issues
    4. Text flow problems
    """
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Original document: {len(content)} characters")
    
    # 1. Fix orphaned numbers at beginning of lines
    content = re.sub(r'\n\d+\.\s*\n', '\n', content)
    content = re.sub(r'\n\d+\s*\n', '\n', content)
    
    # 2. Fix LaTeX escaping issues
    content = content.replace('\\~', '~')
    content = content.replace('\\%', '%')
    
    # 3. Fix awkward line breaks in the middle of sentences
    # Join lines that end with lowercase/comma/semicolon and next line starts with lowercase
    lines = content.split('\n')
    fixed_lines = []
    i = 0
    
    while i < len(lines):
        current_line = lines[i].strip()
        
        # Skip empty lines and LaTeX commands
        if not current_line or current_line.startswith('\\') or current_line.startswith('%'):
            fixed_lines.append(lines[i])
            i += 1
            continue
        
        # Check if this line should be joined with the next
        if i < len(lines) - 1:
            next_line = lines[i + 1].strip()
            
            # Join if current line doesn't end with period/question/exclamation
            # and next line doesn't start with capital letter or LaTeX command
            if (current_line and next_line and 
                not current_line.endswith(('.', '?', '!', ':', ';')) and
                not next_line[0].isupper() and
                not next_line.startswith('\\') and
                not next_line.startswith('%') and
                len(current_line) > 10):  # Avoid joining very short lines
                
                # Join the lines with a space
                combined_line = current_line + ' ' + next_line
                fixed_lines.append(combined_line)
                i += 2  # Skip the next line since we've combined it
                continue
        
        fixed_lines.append(lines[i])
        i += 1
    
    # 4. Rejoin and clean up multiple spaces
    content = '\n'.join(fixed_lines)
    content = re.sub(r' +', ' ', content)  # Multiple spaces to single space
    
    # 5. Fix paragraph breaks (ensure proper spacing between paragraphs)
    content = re.sub(r'\n\n\n+', '\n\n', content)  # Max 2 newlines
    
    # 6. Fix specific formatting issues in enumerated lists
    content = re.sub(r'\n([a-z])\.\s+', r'\n\n\1. ', content)  # Fix list formatting
    
    # 7. Ensure proper spacing around LaTeX environments
    content = re.sub(r'(\w)\n(\\begin)', r'\1\n\n\2', content)
    content = re.sub(r'(\\end\{[^}]+\})\n(\w)', r'\1\n\n\2', content)
    
    # 8. Fix specific patterns we found in the document
    content = re.sub(r'(\w+)\s*\n\s*([a-z])', r'\1 \2', content)
    
    print(f"Fixed document: {len(content)} characters")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Document saved to {output_file}")
    return len(content)

if __name__ == "__main__":
    input_file = "phase1_comprehensive_full_fixed.tex"
    output_file = "phase1_comprehensive_final.tex"
    
    char_count = comprehensive_document_fix(input_file, output_file)
    print(f"\n✅ Comprehensive document fix completed!")
    print(f"📄 Output: {output_file}")
    print(f"📊 Character count: {char_count:,}")

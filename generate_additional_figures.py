#!/usr/bin/env python3
"""
Generate Additional Relevant Figures for Phase 1 Clinical Trials
Creates 3 additional high-quality PNG figures to enhance the document
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Rectangle
import numpy as np
import seaborn as sns

# Set up the figure with high DPI for LaTeX
plt.rcParams.update({
    'font.size': 10,
    'font.family': 'serif',
    'font.serif': ['Times New Roman'],
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'text.usetex': False,
    'axes.linewidth': 1.2,
    'patch.linewidth': 1.2
})

# Figure 1: PK/PD Relationships in Phase 1 Trials
def create_pkpd_figure():
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))
    fig.suptitle('Pharmacokinetic and Pharmacodynamic Relationships in Phase 1 Trials', 
                 fontsize=16, fontweight='bold', y=0.95)
    
    # PK Profile (top left)
    time = np.linspace(0, 24, 100)
    conc = 100 * np.exp(-0.1 * time) * (1 - np.exp(-0.5 * time))
    ax1.plot(time, conc, 'b-', linewidth=3, label='Plasma Concentration')
    ax1.axhline(y=50, color='r', linestyle='--', linewidth=2, label='Therapeutic Window')
    ax1.axhline(y=10, color='orange', linestyle='--', linewidth=2, label='Minimum Effective')
    ax1.fill_between(time, 10, 50, alpha=0.2, color='green', label='Therapeutic Range')
    ax1.set_xlabel('Time (hours)', fontweight='bold')
    ax1.set_ylabel('Concentration (ng/mL)', fontweight='bold')
    ax1.set_title('Single Dose PK Profile', fontweight='bold')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Dose-Response (top right)
    doses = np.array([1, 3, 10, 30, 100, 300])
    response = 100 * doses / (doses + 50)
    ax2.semilogx(doses, response, 'ro-', linewidth=3, markersize=8)
    ax2.axhline(y=50, color='g', linestyle='--', linewidth=2, label='ED50')
    ax2.set_xlabel('Dose (mg)', fontweight='bold')
    ax2.set_ylabel('Response (%)', fontweight='bold')
    ax2.set_title('Dose-Response Relationship', fontweight='bold')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # PK Parameters vs Dose (bottom left)
    doses_pk = np.array([10, 30, 100, 300, 1000])
    cmax = doses_pk * 0.8 + np.random.normal(0, 5, len(doses_pk))
    auc = doses_pk * 12 + np.random.normal(0, 50, len(doses_pk))
    
    ax3_twin = ax3.twinx()
    line1 = ax3.plot(doses_pk, cmax, 'bo-', linewidth=3, markersize=8, label='Cmax')
    line2 = ax3_twin.plot(doses_pk, auc, 'rs-', linewidth=3, markersize=8, label='AUC')
    
    ax3.set_xlabel('Dose (mg)', fontweight='bold')
    ax3.set_ylabel('Cmax (ng/mL)', fontweight='bold', color='blue')
    ax3_twin.set_ylabel('AUC (ng·h/mL)', fontweight='bold', color='red')
    ax3.set_title('Dose Proportionality Assessment', fontweight='bold')
    
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax3.legend(lines, labels, loc='upper left')
    ax3.grid(True, alpha=0.3)
    
    # Safety Margin (bottom right)
    x = np.linspace(0, 1000, 100)
    safety_margin = 1000 / (x + 100)
    therapeutic_index = 500 / (x + 50)
    
    ax4.plot(x, safety_margin, 'r-', linewidth=3, label='Safety Margin')
    ax4.plot(x, therapeutic_index, 'g-', linewidth=3, label='Therapeutic Index')
    ax4.axhline(y=10, color='orange', linestyle='--', linewidth=2, label='Minimum Safety Factor')
    ax4.set_xlabel('Dose (mg)', fontweight='bold')
    ax4.set_ylabel('Safety Ratio', fontweight='bold')
    ax4.set_title('Safety Assessment', fontweight='bold')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('figure7_pkpd_relationships.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()

# Figure 2: Regulatory Timeline Comparison
def create_regulatory_timeline():
    fig, ax = plt.subplots(1, 1, figsize=(16, 10))
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # Title
    ax.text(6, 9.5, 'Regulatory Timeline Comparison: FDA vs EMA vs CDSCO', 
            fontsize=18, fontweight='bold', ha='center')
    
    # Define colors
    colors = {'FDA': '#1f77b4', 'EMA': '#ff7f0e', 'CDSCO': '#2ca02c'}
    
    # Timeline bars for each agency
    agencies = ['FDA (US)', 'EMA (EU)', 'CDSCO (India)']
    y_positions = [7.5, 5.5, 3.5]
    
    for i, (agency, y_pos) in enumerate(zip(agencies, y_positions)):
        color = list(colors.values())[i]
        
        # Agency label
        ax.text(0.5, y_pos + 0.3, agency, fontsize=14, fontweight='bold', 
                ha='center', va='center')
        
        # Timeline phases
        if 'FDA' in agency:
            # IND submission to review
            rect1 = Rectangle((1, y_pos), 2, 0.4, facecolor=color, alpha=0.7, edgecolor='black')
            ax.add_patch(rect1)
            ax.text(2, y_pos + 0.2, 'IND Review\n(30 days)', ha='center', va='center', fontsize=9)
            
            # Phase 1 conduct
            rect2 = Rectangle((3.5, y_pos), 4, 0.4, facecolor=color, alpha=0.5, edgecolor='black')
            ax.add_patch(rect2)
            ax.text(5.5, y_pos + 0.2, 'Phase 1 Conduct\n(6-18 months)', ha='center', va='center', fontsize=9)
            
            # Reporting
            rect3 = Rectangle((8, y_pos), 2, 0.4, facecolor=color, alpha=0.3, edgecolor='black')
            ax.add_patch(rect3)
            ax.text(9, y_pos + 0.2, 'Reporting\n(3-6 months)', ha='center', va='center', fontsize=9)
            
        elif 'EMA' in agency:
            # CTA submission to approval
            rect1 = Rectangle((1, y_pos), 2.5, 0.4, facecolor=color, alpha=0.7, edgecolor='black')
            ax.add_patch(rect1)
            ax.text(2.25, y_pos + 0.2, 'CTA Review\n(60 days)', ha='center', va='center', fontsize=9)
            
            # Phase 1 conduct
            rect2 = Rectangle((4, y_pos), 4, 0.4, facecolor=color, alpha=0.5, edgecolor='black')
            ax.add_patch(rect2)
            ax.text(6, y_pos + 0.2, 'Phase 1 Conduct\n(8-20 months)', ha='center', va='center', fontsize=9)
            
            # Reporting
            rect3 = Rectangle((8.5, y_pos), 1.5, 0.4, facecolor=color, alpha=0.3, edgecolor='black')
            ax.add_patch(rect3)
            ax.text(9.25, y_pos + 0.2, 'Reporting\n(6 months)', ha='center', va='center', fontsize=9)
            
        else:  # CDSCO
            # IND submission to approval
            rect1 = Rectangle((1, y_pos), 3, 0.4, facecolor=color, alpha=0.7, edgecolor='black')
            ax.add_patch(rect1)
            ax.text(2.5, y_pos + 0.2, 'IND Review\n(90 days)', ha='center', va='center', fontsize=9)
            
            # Phase 1 conduct
            rect2 = Rectangle((4.5, y_pos), 3.5, 0.4, facecolor=color, alpha=0.5, edgecolor='black')
            ax.add_patch(rect2)
            ax.text(6.25, y_pos + 0.2, 'Phase 1 Conduct\n(6-15 months)', ha='center', va='center', fontsize=9)
            
            # Reporting
            rect3 = Rectangle((8.5, y_pos), 1.5, 0.4, facecolor=color, alpha=0.3, edgecolor='black')
            ax.add_patch(rect3)
            ax.text(9.25, y_pos + 0.2, 'Reporting\n(4 months)', ha='center', va='center', fontsize=9)
    
    # Timeline scale
    for month in range(1, 11):
        ax.axvline(x=month, color='gray', linestyle=':', alpha=0.5)
        ax.text(month, 2.5, f'{month}', ha='center', va='center', fontsize=8)
    
    ax.text(5.5, 2, 'Timeline (months)', ha='center', va='center', fontsize=12, fontweight='bold')
    
    # Key differences box
    key_text = """Key Regulatory Differences:
    • FDA: 30-day automatic approval unless clinical hold
    • EMA: Explicit approval required (60 days)
    • CDSCO: Explicit approval required (90 days)
    • All require GCP compliance and safety reporting
    • EMA emphasizes risk mitigation (post-TGN1412)
    • CDSCO fast-tracks domestic innovations"""
    
    ax.text(6, 1, key_text, ha='center', va='center', fontsize=10, 
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('figure8_regulatory_timeline.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()

# Figure 3: Risk Assessment Matrix
def create_risk_matrix():
    fig, ax = plt.subplots(1, 1, figsize=(12, 10))
    
    # Create risk matrix data
    compounds = ['Small Molecule\n(Known Target)', 'Small Molecule\n(Novel Target)', 
                'Biologic\n(Known Target)', 'Biologic\n(Novel Target)', 
                'First-in-Class\n(Novel MOA)', 'Oncology\n(Cytotoxic)',
                'Gene Therapy', 'Cell Therapy']
    
    risk_levels = ['Low', 'Medium', 'High', 'Very High']
    
    # Risk matrix (compound type vs risk level)
    risk_data = np.array([
        [1, 0, 0, 0],  # Small Molecule (Known Target)
        [0, 1, 0, 0],  # Small Molecule (Novel Target)
        [0, 1, 0, 0],  # Biologic (Known Target)
        [0, 0, 1, 0],  # Biologic (Novel Target)
        [0, 0, 1, 0],  # First-in-Class
        [0, 0, 0, 1],  # Oncology (Cytotoxic)
        [0, 0, 0, 1],  # Gene Therapy
        [0, 0, 0, 1],  # Cell Therapy
    ])
    
    # Create heatmap
    sns.heatmap(risk_data, annot=True, cmap='RdYlGn_r', cbar_kws={'label': 'Risk Level'},
                xticklabels=risk_levels, yticklabels=compounds, ax=ax, 
                linewidths=1, linecolor='black')
    
    ax.set_title('Risk Assessment Matrix for Phase 1 Compounds', 
                 fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('Risk Level', fontsize=12, fontweight='bold')
    ax.set_ylabel('Compound Type', fontsize=12, fontweight='bold')
    
    # Add risk mitigation strategies
    strategies_text = """Risk Mitigation Strategies:
    Low Risk: Standard 3+3 design, healthy volunteers
    Medium Risk: Enhanced monitoring, sentinel dosing
    High Risk: MABEL approach, extensive preclinical data
    Very High Risk: Patient population only, intensive monitoring"""
    
    ax.text(4.5, -1.5, strategies_text, ha='left', va='top', fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('figure9_risk_assessment.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()

# Generate all figures
if __name__ == "__main__":
    print("Generating additional Phase 1 clinical trial figures...")
    
    create_pkpd_figure()
    print("Figure 7 (PK/PD Relationships) generated successfully")
    
    create_regulatory_timeline()
    print("Figure 8 (Regulatory Timeline) generated successfully")
    
    create_risk_matrix()
    print("Figure 9 (Risk Assessment Matrix) generated successfully")
    
    print("All additional figures generated successfully!")

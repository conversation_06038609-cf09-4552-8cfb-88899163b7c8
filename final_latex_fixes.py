import re

def final_latex_fixes(input_file, output_file):
    """
    Fix remaining LaTeX compilation issues:
    1. Replace Unicode characters with LaTeX equivalents
    2. Fix broken figure references
    3. Fix header height issue
    """
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Fixing final LaTeX issues: {len(content)} characters")
    
    # 1. Replace Unicode mathematical symbols with LaTeX equivalents
    content = content.replace('≥', r'$\geq$')
    content = content.replace('≤', r'$\leq$')
    content = content.replace('±', r'$\pm$')
    content = content.replace('×', r'$\times$')
    content = content.replace('→', r'$\rightarrow$')
    content = content.replace('←', r'$\leftarrow$')
    content = content.replace('↔', r'$\leftrightarrow$')
    
    # 2. Fix broken figure reference
    content = re.sub(r'Figure~\\ref\{fig:\\1\}', r'Figure~\\ref{fig:dose_escalation}', content)
    
    # 3. Fix header height issue by adding to preamble
    header_fix = '\\setlength{\\headheight}{14pt}'
    
    # Insert after geometry package
    geometry_pattern = '\\geometry{margin=1in}'
    if geometry_pattern in content:
        content = content.replace(geometry_pattern, geometry_pattern + '\n' + header_fix)
    
    # 4. Fix any other potential LaTeX issues
    
    # Ensure proper spacing around math mode
    content = re.sub(r'(\w)\$', r'\1 $', content)  # Space before math
    content = re.sub(r'\$(\w)', r'$ \1', content)  # Space after math
    
    # Fix any remaining double spaces
    content = re.sub(r' +', ' ', content)
    
    print(f"Fixed document: {len(content)} characters")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Final document saved to {output_file}")
    return len(content)

if __name__ == "__main__":
    input_file = "phase1_comprehensive_perfect.tex"
    output_file = "phase1_comprehensive_final.tex"
    
    char_count = final_latex_fixes(input_file, output_file)
    print(f"\n✅ Final LaTeX fixes completed!")
    print(f"📄 Output: {output_file}")
    print(f"📊 Character count: {char_count:,}")
    print(f"\n🔧 Issues Fixed:")
    print(f"  ✅ Unicode mathematical symbols")
    print(f"  ✅ Broken figure references")
    print(f"  ✅ Header height warnings")
    print(f"  ✅ LaTeX formatting issues")

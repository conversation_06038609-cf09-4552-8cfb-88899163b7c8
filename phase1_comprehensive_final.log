This is pdfTeX, Version 3.141592653-2.6-1.40.27 (MiKTeX 25.4) (preloaded format=pdflatex 2025.6.21)  13 JUL 2025 23:14
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./phase1_comprehensive_final.tex
(phase1_comprehensive_final.tex
LaTeX2e <2025-06-01>
L3 programming layer <2025-05-26>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size11.clo
File: size11.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count275
\c@section=\count276
\c@subsection=\count277
\c@subsubsection=\count278
\c@paragraph=\count279
\c@subparagraph=\count280
\c@figure=\count281
\c@table=\count282
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2024/12/21 v2.1c Standard LaTeX package
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count283
\Gm@cntv=\count284
\c@Gm@tempcnt=\count285
\Gm@bindingoffset=\dimen149
\Gm@wd@mp=\dimen150
\Gm@odd@mp=\dimen151
\Gm@even@mp=\dimen152
\Gm@layoutwidth=\dimen153
\Gm@layoutheight=\dimen154
\Gm@layouthoffset=\dimen155
\Gm@layoutvoffset=\dimen156
\Gm@dimlist=\toks20

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.cfg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2024/12/31 v1.2e Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphics.c
fg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen157
\Gin@req@width=\dimen158
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count286
\float@exts=\toks21
\float@box=\box53
\@float@everytoks=\toks22
\@floatcapt=\box54
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen159
\captionmargin=\dimen160
\caption@leftmargin=\dimen161
\caption@rightmargin=\dimen162
\caption@width=\dimen163
\caption@indent=\dimen164
\caption@parindent=\dimen165
\caption@hangindent=\dimen166
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count287
\c@continuedfloat=\count288
Package caption Info: float package is loaded.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count289
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count290
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2025/05/18 v2.17x AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2024/11/17 v2.01 AMS text

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks23
\ex@=\dimen167
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen168
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count291
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count292
\leftroot@=\count293
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count294
\DOTSCASE@=\count295
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box55
\strutbox@=\box56
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen169
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count296
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count297
\dotsspace@=\muskip17
\c@parentequation=\count298
\dspbrk@lvl=\count299
\tag@help=\toks24
\row@=\count300
\column@=\count301
\maxfields@=\count302
\andhelp@=\toks25
\eqnshift@=\dimen170
\alignsep@=\dimen171
\tagshift@=\dimen172
\tagwidth@=\dimen173
\totwidth@=\dimen174
\lineht@=\dimen175
\@envbody=\toks26
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks27
LaTeX Info: Redefining \[ on input line 2949.
LaTeX Info: Redefining \] on input line 2950.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen176
\lightrulewidth=\dimen177
\cmidrulewidth=\dimen178
\belowrulesep=\dimen179
\belowbottomsep=\dimen180
\aboverulesep=\dimen181
\abovetopsep=\dimen182
\cmidrulesep=\dimen183
\cmidrulekern=\dimen184
\defaultaddspace=\dimen185
\@cmidla=\count303
\@cmidlb=\count304
\@aboverulesep=\dimen186
\@belowrulesep=\dimen187
\@thisruleclass=\count305
\@lastruleclass=\count306
\@thisrulewidth=\dimen188
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2025-05-20 v7.01m Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvdefine
keys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfescape.s
ty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftexcmds
.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infwarerr.s
ty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\gettit
lestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count307
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count308
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringenc.s
ty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen189
\Hy@linkcounter=\count309
\Hy@pagecounter=\count310
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2025-05-20 v7.01m Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count311
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2025-05-20 v7.01m Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count312

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen190

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigintcalc
.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count313
\Field@Width=\dimen191
\Fld@charsize=\dimen192
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count314
\c@Item=\count315
\c@Hfootnote=\count316
)
Package hyperref Info: Driver (autodetected): hpdftex.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.def
File: hpdftex.def 2025-05-20 v7.01m Hyperref driver for pdfTeX
\Fld@listcount=\count317
\c@bookmark@seq@number=\count318

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\rerunfil
echeck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uniquec
ounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip54
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/enumitem\enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip55
\enit@outerparindent=\dimen193
\enit@toks=\toks28
\enit@inbox=\box57
\enit@count@id=\count319
\enitdp@description=\count320
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/frontendlayer\tikz.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgf.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfrcs.st
y
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfutil
-common.tex
\pgfutil@everybye=\toks29
\pgfutil@tempdima=\dimen194
\pgfutil@tempdimb=\dimen195
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfutil
-latex.def
\pgfutil@abb=\box58
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfrcs.
code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf\pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgfcore.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/systemlayer\pgfsys.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys
.code.tex
\pgfkeys@pathtoks=\toks30
\pgfkeys@temptoks=\toks31

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys
libraryfiltered.code.tex
\pgfkeys@tmptoks=\toks32
))
\pgf@x=\dimen196
\pgf@y=\dimen197
\pgf@xa=\dimen198
\pgf@ya=\dimen199
\pgf@xb=\dimen256
\pgf@yb=\dimen257
\pgf@xc=\dimen258
\pgf@yc=\dimen259
\pgf@xd=\dimen260
\pgf@yd=\dimen261
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count321
\c@pgf@countb=\count322
\c@pgf@countc=\count323
\c@pgf@countd=\count324
\t@pgf@toka=\toks33
\t@pgf@tokb=\toks34
\t@pgf@tokc=\toks35
\pgf@sys@id@count=\count325

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgf.c
fg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
ssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count326
\pgfsyssoftpath@bigbuffer@items=\count327
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
sprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
e.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmath.code
.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathutil.
code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathparse
r.code.tex
\pgfmath@dimen=\dimen262
\pgfmath@count=\count328
\pgfmath@box=\box59
\pgfmath@toks=\toks36
\pgfmath@stack@operand=\toks37
\pgfmath@stack@operation=\toks38
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.basic.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.trigonometric.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.random.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.comparison.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.base.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.round.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.misc.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.integerarithmetics.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathcalc.
code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfloat
.code.tex
\c@pgfmathroundto@lastzeros=\count329
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfint.code.
tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen263
\pgf@picmaxx=\dimen264
\pgf@picminy=\dimen265
\pgf@picmaxy=\dimen266
\pgf@pathminx=\dimen267
\pgf@pathmaxx=\dimen268
\pgf@pathminy=\dimen269
\pgf@pathmaxy=\dimen270
\pgf@xx=\dimen271
\pgf@xy=\dimen272
\pgf@yx=\dimen273
\pgf@yy=\dimen274
\pgf@zx=\dimen275
\pgf@zy=\dimen276
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen277
\pgf@path@lasty=\dimen278
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen279
\pgf@shorten@start@additional=\dimen280
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
escopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box60
\pgf@hbox=\box61
\pgf@layerbox@main=\box62
\pgf@picture@serial@count=\count330
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
egraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen281
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
etransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen282
\pgf@pt@y=\dimen283
\pgf@pt@temp=\dimen284
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
equick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
earrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen285
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen286
\pgf@sys@shading@range@num=\count331
\pgf@shadingcount=\count332
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box63
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
elayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
etransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
erdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
shapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box64
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
plot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\pgfco
mp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen287
\pgf@nodesepend=\dimen288
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\pgfco
mp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgffor.st
y
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfkeys.s
ty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys
.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/math\pgfmath.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmath.code
.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgffor.
code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen289
\pgffor@skip=\dimen290
\pgffor@stack=\toks39
\pgffor@toks=\toks40
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z\tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries\pgflibr
aryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count333
\pgfplotmarksize=\dimen291
)
\tikz@lastx=\dimen292
\tikz@lasty=\dimen293
\tikz@lastxsaved=\dimen294
\tikz@lastysaved=\dimen295
\tikz@lastmovetox=\dimen296
\tikz@lastmovetoy=\dimen297
\tikzleveldistance=\dimen298
\tikzsiblingdistance=\dimen299
\tikz@figbox=\box65
\tikz@figbox@bg=\box66
\tikz@tempbox=\box67
\tikz@tempbox@bg=\box68
\tikztreelevel=\count334
\tikznumberofchildren=\count335
\tikznumberofcurrentchild=\count336
\tikz@fig@count=\count337

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
matrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count338
\pgfmatrixcurrentcolumn=\count339
\pgf@matrix@numberofcolumns=\count340
)
\tikz@expandcount=\count341

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibraryshapes.geometric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/shapes\
pgflibraryshapes.geometric.code.tex
File: pgflibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibraryshapes.code.tex
File: tikzlibraryshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibraryshapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/shapes\
pgflibraryshapes.misc.code.tex
File: pgflibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibraryshapes.symbols.code.tex
File: tikzlibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/shapes\
pgflibraryshapes.symbols.code.tex
File: pgflibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibraryshapes.arrows.code.tex
File: tikzlibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/shapes\
pgflibraryshapes.arrows.code.tex
File: pgflibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibraryshapes.callouts.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/shapes\
pgflibraryshapes.callouts.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibraryshapes.multipart.code.tex
File: tikzlibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/shapes\
pgflibraryshapes.multipart.code.tex
File: pgflibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodepartlowerbox=\box69
\pgfnodeparttwobox=\box70
\pgfnodepartthreebox=\box71
\pgfnodepartfourbox=\box72
\pgfnodeparttwentybox=\box73
\pgfnodepartnineteenbox=\box74
\pgfnodeparteighteenbox=\box75
\pgfnodepartseventeenbox=\box76
\pgfnodepartsixteenbox=\box77
\pgfnodepartfifteenbox=\box78
\pgfnodepartfourteenbox=\box79
\pgfnodepartthirteenbox=\box80
\pgfnodeparttwelvebox=\box81
\pgfnodepartelevenbox=\box82
\pgfnodeparttenbox=\box83
\pgfnodepartninebox=\box84
\pgfnodeparteightbox=\box85
\pgfnodepartsevenbox=\box86
\pgfnodepartsixbox=\box87
\pgfnodepartfivebox=\box88
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibrarypositioning.code.tex
File: tikzlibrarypositioning.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgfplots\pgfplots.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.rev
ision.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.cod
e.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotscore
.code.tex
\t@pgfplots@toka=\toks41
\t@pgfplots@tokb=\toks42
\t@pgfplots@tokc=\toks43
\pgfplots@tmpa=\dimen300
\c@pgfplots@coordindex=\count342
\c@pgfplots@scanlineindex=\count343

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/sys\pgfplots
sysgeneric.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/libs\pgfplot
slibrary.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/oldpgfcompat
ib\pgfplotsoldpgfsupp_loader.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries\pgflibr
aryfpu.code.tex)
Package pgfplots: loading complementary utilities for your pgf version...
\t@pgf@toka=\toks44
\t@pgf@tokb=\toks45
\t@pgf@tokc=\toks46

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/oldpgfcompat
ib\pgfplotsoldpgfsupp_pgfutil-common-lists.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplot
sutil.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructur
e\pgfplotsliststructure.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructur
e\pgfplotsliststructureext.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructur
e\pgfplotsarray.code.tex
\c@pgfplotsarray@tmp=\count344
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructur
e\pgfplotsmatrix.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/numtable\pgf
plotstableshared.code.tex
\c@pgfplotstable@counta=\count345
\t@pgfplotstable@a=\toks47
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructur
e\pgfplotsdeque.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplot
sbinary.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplot
sbinary.data.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplot
sutil.verb.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/libs\pgflibr
arypgfplots.surfshading.code.tex
\c@pgfplotslibrarysurf@no=\count346

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/sys\pgflibra
rypgfplots.surfshading.pgfsys-pdftex.def)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplot
scolormap.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplot
scolor.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotsstac
kedplots.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotsplot
handlers.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotsmesh
plothandler.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotsmesh
plotimage.code.tex)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.sca
ling.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotscoor
dprocessing.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.err
orbars.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.mar
kers.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotstick
s.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.pat
hs.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibrarydecorations.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
decorations.code.tex
\pgfdecoratedcompleteddistance=\dimen301
\pgfdecoratedremainingdistance=\dimen302
\pgfdecoratedinputsegmentcompleteddistance=\dimen303
\pgfdecoratedinputsegmentremainingdistance=\dimen304
\pgf@decorate@distancetomove=\dimen305
\pgf@decorate@repeatstate=\count347
\pgfdecorationsegmentamplitude=\dimen306
\pgfdecorationsegmentlength=\dimen307
)
\tikz@lib@dec@box=\box89
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibrarydecorations.pathmorphing.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/decorat
ions\pgflibrarydecorations.pathmorphing.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibrarydecorations.pathreplacing.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/decorat
ions\pgflibrarydecorations.pathreplacing.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/libs\tikzlib
rarypgfplots.contourlua.code.tex)
\pgfplots@numplots=\count348
\pgfplots@xmin@reg=\dimen308
\pgfplots@xmax@reg=\dimen309
\pgfplots@ymin@reg=\dimen310
\pgfplots@ymax@reg=\dimen311
\pgfplots@zmin@reg=\dimen312
\pgfplots@zmax@reg=\dimen313
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tik
z/libraries\tikzlibraryplotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries\pgflibr
aryplotmarks.code.tex
File: pgflibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fancyhdr\fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers

\f@nch@headwidth=\skip56
\f@nch@offset@elh=\skip57
\f@nch@offset@erh=\skip58
\f@nch@offset@olh=\skip59
\f@nch@offset@orh=\skip60
\f@nch@offset@elf=\skip61
\f@nch@offset@erf=\skip62
\f@nch@offset@olf=\skip63
\f@nch@offset@orf=\skip64
\f@nch@height=\skip65
\f@nch@footalignment=\skip66
\f@nch@widthL=\skip67
\f@nch@widthC=\skip68
\f@nch@widthR=\skip69
\@temptokenb=\toks48
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/titlesec\titlesec.sty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box90
\beforetitleunit=\skip70
\aftertitleunit=\skip71
\ttl@plus=\dimen314
\ttl@minus=\dimen315
\ttl@toksa=\toks49
\titlewidth=\dimen316
\titlewidthlast=\dimen317
\titlewidthfirst=\dimen318
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\longtable.sty
Package: longtable 2024-12-18 v4.23 Multi-page Table package (DPC)
\LTleft=\skip72
\LTright=\skip73
\LTpre=\skip74
\LTpost=\skip75
\LTchunksize=\count349
\LTcapwidth=\dimen319
\LT@head=\box91
\LT@firsthead=\box92
\LT@foot=\box93
\LT@lastfoot=\box94
\LT@gbox=\box95
\LT@cols=\count350
\LT@rows=\count351
\c@LT@tables=\count352
\c@LT@chunks=\count353
\LT@p@ftn=\toks50
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/multirow\multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip76
\multirow@cntb=\count354
\multirow@dima=\skip77
\bigstrutjot=\dimen320
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/parskip\parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments
)
Package hyperref Info: Option `colorlinks' set `true' on input line 57.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend-pdf
tex.def
File: l3backend-pdftex.def 2025-04-14 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count355
) (phase1_comprehensive_final.aux)
\openout1 = `phase1_comprehensive_final.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 64.
LaTeX Font Info:    ... okay on input line 64.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 64.
LaTeX Font Info:    ... okay on input line 64.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 64.
LaTeX Font Info:    ... okay on input line 64.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 64.
LaTeX Font Info:    ... okay on input line 64.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 64.
LaTeX Font Info:    ... okay on input line 64.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 64.
LaTeX Font Info:    ... okay on input line 64.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 64.
LaTeX Font Info:    ... okay on input line 64.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 64.
LaTeX Font Info:    ... okay on input line 64.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 64.
LaTeX Font Info:    ... okay on input line 64.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 452.9679pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 700.50687pt, 72.26999pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=452.9679pt
* \textheight=700.50687pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=14.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=50.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pdf.mk
ii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count356
\scratchdimen=\dimen321
\scratchbox=\box96
\nofMPsegments=\count357
\nofMParguments=\count358
\everyMPshowfont=\toks51
\MPscratchCnt=\count359
\MPscratchDim=\dimen322
\MPnumerator=\count360
\makeMPintoPDFobject=\count361
\everyMPtoPDFconversion=\toks52
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstopdf-b
ase.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-sys.c
fg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: hyperref package is loaded.
Package caption Info: longtable package is loaded.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\ltcaption.sty
Package: ltcaption 2021/01/08 v1.4c longtable captions (AR)
)
Package caption Info: End \AtBeginDocument code.
Package hyperref Info: Link coloring ON on input line 64.

(phase1_comprehensive_final.out) (phase1_comprehensive_final.out
! I can't write on file `phase1_comprehensive_final.pdf'.
(Press Enter to retry, or Control-C to exit; default file extension is `.pdf')
Please type another file name for output
! Emergency stop.
\Hy@OutlineName ...utline goto name{#2}count#3{#4}
                                                  
l.1 ...\000o\000d\000u\000c\000t\000i\000o\000n}{}
                                                  % 1
*** (job aborted, file error in nonstop mode)

 
Here is how much of TeX's memory you used:
 35306 strings out of 468225
 829393 string characters out of 5432973
 1367499 words of memory out of 5000000
 63310 multiletter control sequences out of 15000+600000
 627894 words of font info for 42 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 84i,1n,91p,1022b,609s stack positions out of 10000i,1000n,20000p,200000b,200000s
!  ==> Fatal error occurred, no output PDF file produced!

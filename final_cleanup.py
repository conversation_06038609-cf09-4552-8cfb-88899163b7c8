import re

def final_document_cleanup(input_file, output_file):
    """
    Final cleanup to address remaining formatting issues:
    1. Remove empty list items
    2. Remove orphaned numbers
    3. Fix incomplete sections
    4. Clean up any remaining formatting artifacts
    """
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Starting final cleanup: {len(content)} characters")
    
    # 1. Remove empty itemize blocks
    content = re.sub(r'\\begin\{itemize\}\s*(?:\\item\s*)*\\end\{itemize\}', '', content)
    
    # 2. Remove empty list items
    content = re.sub(r'\\item\s*\n(?=\\item|\\end\{itemize\})', '', content)
    content = re.sub(r'\\item\s*$', '', content, flags=re.MULTILINE)
    
    # 3. Remove orphaned single numbers on their own lines
    content = re.sub(r'\n\s*\d+\s*\n', '\n\n', content)
    
    # 4. Fix incomplete sections (lines ending with incomplete subsection commands)
    content = re.sub(r'\\subsection\{but commonly used approaches are\}', r'\\subsection{Commonly Used Approaches}', content)
    
    # 5. Remove any trailing incomplete list items at end of itemize blocks
    content = re.sub(r'\\item\s*\n\\end\{itemize\}', r'\\end{itemize}', content)
    
    # 6. Clean up any double spaces that might have been created
    content = re.sub(r'  +', ' ', content)
    
    # 7. Ensure proper paragraph spacing
    content = re.sub(r'\n\n\n+', '\n\n', content)
    
    # 8. Fix any incomplete itemize environments that are left hanging
    content = re.sub(r'\\begin\{itemize\}\s*\\item\s*$', '', content, flags=re.MULTILINE)
    
    print(f"Final document: {len(content)} characters")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Final document saved to {output_file}")
    return len(content)

if __name__ == "__main__":
    input_file = "phase1_comprehensive_final.tex"
    output_file = "phase1_comprehensive_clean.tex"
    
    char_count = final_document_cleanup(input_file, output_file)
    print(f"\n✅ Final cleanup completed!")
    print(f"📄 Output: {output_file}")
    print(f"📊 Character count: {char_count:,}")

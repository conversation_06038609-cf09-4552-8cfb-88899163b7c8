import os
import re
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib.patches import Rectangle, FancyBboxPatch
import matplotlib.patches as mpatches

def create_comprehensive_latex_document(text):
    """Convert extracted text to comprehensive LaTeX format preserving all content"""
    
    # Clean and format the text
    lines = text.split('\n')
    
    latex_content = r"""\documentclass[11pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{geometry}
\usepackage{graphicx}
\usepackage{float}
\usepackage{caption}
\usepackage{subcaption}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{booktabs}
\usepackage{hyperref}
\usepackage{enumitem}
\usepackage{tikz}
\usepackage{pgfplots}
\usepackage{xcolor}
\usepackage{fancyhdr}
\usepackage{titlesec}
\usepackage{longtable}
\usepackage{array}
\usepackage{multirow}
\usepackage{footnote}

\pgfplotsset{compat=1.17}

\geometry{margin=1in}
\setlength{\parindent}{0pt}
\setlength{\parskip}{0.5em}

% Custom colors
\definecolor{sectioncolor}{RGB}{0,102,204}
\definecolor{subsectioncolor}{RGB}{0,153,76}
\definecolor{subsubsectioncolor}{RGB}{204,102,0}

% Section formatting
\titleformat{\section}{\Large\bfseries\color{sectioncolor}}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries\color{subsectioncolor}}{\thesubsection}{1em}{}
\titleformat{\subsubsection}{\normalsize\bfseries\color{subsubsectioncolor}}{\thesubsubsection}{1em}{}

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{Phase 1 Clinical Trials: Comprehensive Overview}
\fancyhead[R]{\thepage}
\renewcommand{\headrulewidth}{0.4pt}

\title{\textbf{Phase 1 Clinical Trials: A Comprehensive Overview for Clinical Pharmacologists}}
\author{}
\date{}

\begin{document}

\maketitle
\newpage

\tableofcontents
\newpage

"""
    
    # Process the text content systematically
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Skip empty lines
        if not line:
            i += 1
            continue
            
        # Skip title lines as they're already in the header
        if line in ["Phase 1 Clinical Trials: A Comprehensive Overview", "for Clinical Pharmacologists"]:
            i += 1
            continue
        
        # Handle major sections
        if line in ["Introduction", "Objectives of Phase 1 Trials", "Key Steps in Planning and Conducting a Phase 1 Study", 
                   "Conclusion"]:
            latex_content += f"\\section{{{line}}}\n\n"
            i += 1
            continue
            
        # Handle numbered sections (like "1. Preclinical Evaluations...")
        if re.match(r'^\d+\.\s+', line):
            subsection_title = re.sub(r'^\d+\.\s+', '', line).rstrip(':')
            latex_content += f"\\subsection{{{subsection_title}}}\n\n"
            i += 1
            continue
            
        # Handle lettered subsections (like "a. Single Ascending Dose...")
        if re.match(r'^[a-z]\.\s+', line):
            subsubsection_title = re.sub(r'^[a-z]\.\s+', '', line).rstrip(':')
            latex_content += f"\\subsubsection{{{subsubsection_title}}}\n\n"
            i += 1
            continue
            
        # Handle bullet points
        if line.startswith('•'):
            # Start itemize environment
            latex_content += "\\begin{itemize}\n"
            while i < len(lines) and lines[i].strip().startswith('•'):
                item_text = lines[i].strip()[1:].strip()
                # Clean up LaTeX special characters
                item_text = clean_latex_text(item_text)
                latex_content += f"\\item {item_text}\n"
                i += 1
            latex_content += "\\end{itemize}\n\n"
            continue
            
        # Handle figure placeholders
        if line.startswith('Figure:'):
            figure_title = line[7:].strip()
            if "Integrated Phase 1 Study Design" in figure_title:
                latex_content += create_integrated_study_design_figure()
            elif "Dose Escalation in Oncology" in figure_title:
                latex_content += create_dose_escalation_figure()
            else:
                latex_content += f"\\begin{{figure}}[H]\n\\centering\n\\includegraphics[width=0.8\\textwidth]{{placeholder.png}}\n\\caption{{{figure_title}}}\n\\end{{figure}}\n\n"
            i += 1
            continue
            
        # Handle figure references
        if re.search(r'Figure \d+', line):
            # Replace figure reference with proper LaTeX reference
            line = re.sub(r'Figure (\d+)', r'Figure~\\ref{fig:\\1}', line)
            line = clean_latex_text(line)
            latex_content += f"{line}\n\n"
            i += 1
            continue
            
        # Handle regular text paragraphs
        # Clean up the text
        line = clean_latex_text(line)
        
        # Check if this is a section heading that we missed
        if line.endswith(':') and len(line) < 100 and not line.startswith('('):
            # This might be a section heading
            section_title = line.rstrip(':')
            latex_content += f"\\subsection{{{section_title}}}\n\n"
        else:
            latex_content += f"{line}\n\n"
        
        i += 1
    
    # Add figures where they would naturally fit
    latex_content = add_figures_to_content(latex_content)
    
    latex_content += "\\end{document}"
    
    return latex_content

def clean_latex_text(text):
    """Clean text for LaTeX formatting"""
    # Replace special characters
    text = text.replace('_', '\\_')
    text = text.replace('%', '\\%')
    text = text.replace('&', '\\&')
    text = text.replace('#', '\\#')
    text = text.replace('$', '\\$')
    
    # Handle mathematical expressions
    text = re.sub(r'C_max', r'$C_{max}$', text)
    text = re.sub(r'T_1/2', r'$T_{1/2}$', text)
    text = re.sub(r'T_max', r'$T_{max}$', text)
    text = re.sub(r'AUC', r'$AUC$', text)
    
    # Handle special formatting
    text = re.sub(r'\b(Phase \d+)\b', r'\\textbf{\\1}', text)
    text = re.sub(r'\b(IND|CTA|FDA|EMA|GCP|MTD|RP2D|NOAEL|MABEL)\b', r'\\textbf{\\1}', text)
    
    return text

def add_figures_to_content(content):
    """Add figures at appropriate locations in the content"""
    
    # Add PK profiles figure after pharmacokinetic discussion
    pk_insertion_point = content.find("pharmacokinetic")
    if pk_insertion_point != -1:
        # Find the end of the current paragraph
        next_section = content.find("\\section", pk_insertion_point)
        if next_section != -1:
            content = content[:next_section] + create_pk_profile_figure() + content[next_section:]
    
    # Add safety monitoring figure after safety discussion
    safety_insertion_point = content.find("Safety Monitoring")
    if safety_insertion_point != -1:
        next_section = content.find("\\section", safety_insertion_point)
        if next_section != -1:
            content = content[:next_section] + create_safety_monitoring_figure() + content[next_section:]
    
    return content

def create_integrated_study_design_figure():
    """Create a figure showing integrated Phase 1 study design"""
    return r"""
\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{integrated_phase1_design.png}
\caption{Integrated Phase 1 Study Design: Modern trials often combine several Phase 1 elements adaptively. The schematic shows how a single protocol can encompass SAD, MAD, and additional evaluations.}
\label{fig:integrated_design}
\end{figure}

"""

def create_dose_escalation_figure():
    """Create a figure showing dose escalation methods"""
    return r"""
\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{dose_escalation_methods.png}
\caption{Dose Escalation in Oncology: Comparison of (a) the rule-based "3+3" design and (b) the model-based Continual Reassessment Method (CRM). The 3+3 design uses fixed rules for dose escalation, while CRM uses a Bayesian model to estimate the dose-toxicity curve and recommend optimal doses.}
\label{fig:dose_escalation}
\end{figure}

"""

def create_pk_profile_figure():
    """Create a pharmacokinetic profile figure"""
    return r"""
\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{pk_profiles.png}
\caption{Pharmacokinetic Profiles: (a) Single dose concentration-time profiles showing dose proportionality across different dose levels, and (b) Multiple dose profile showing accumulation to steady-state with daily dosing.}
\label{fig:pk_profiles}
\end{figure}

"""

def create_safety_monitoring_figure():
    """Create a figure showing safety monitoring in Phase 1"""
    return r"""
\begin{figure}[H]
\centering
\includegraphics[width=0.8\textwidth]{safety_monitoring.png}
\caption{Phase 1 Safety Monitoring Timeline: Showing the intensive monitoring schedule from screening through follow-up, with key safety review points highlighted.}
\label{fig:safety_monitoring}
\end{figure}

"""

def main():
    """Main function to convert PDF to comprehensive LaTeX with all content preserved"""
    
    # Read the original extracted text
    with open('original_pdf_text.txt', 'r', encoding='utf-8') as f:
        original_text = f.read()
    
    print("Creating comprehensive LaTeX document...")
    print(f"Processing {len(original_text)} characters from original document...")
    
    # Create comprehensive LaTeX document
    latex_content = create_comprehensive_latex_document(original_text)
    
    # Write LaTeX file
    with open('phase1_clinical_trials_comprehensive.tex', 'w', encoding='utf-8') as f:
        f.write(latex_content)
    
    print("Comprehensive LaTeX file created: phase1_clinical_trials_comprehensive.tex")
    print(f"Output LaTeX file has {len(latex_content)} characters")
    
    # Create a compilation script
    compile_script = r'''
@echo off
echo ======================================
echo  Comprehensive Phase 1 Clinical Trials LaTeX Compiler
echo ======================================
echo.

echo Checking for LaTeX installation...
where pdflatex >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: pdflatex not found. Please install a LaTeX distribution.
    pause
    exit /b 1
)

echo LaTeX found. Compiling comprehensive document...
echo.

echo First compilation pass...
pdflatex -interaction=nonstopmode phase1_clinical_trials_comprehensive.tex
if %errorlevel% neq 0 (
    echo WARNING: First compilation had issues. Continuing...
)

echo Second compilation pass (for cross-references)...
pdflatex -interaction=nonstopmode phase1_clinical_trials_comprehensive.tex
if %errorlevel% neq 0 (
    echo WARNING: Second compilation had issues. Continuing...
)

echo Third compilation pass (for final references)...
pdflatex -interaction=nonstopmode phase1_clinical_trials_comprehensive.tex

echo.
echo ======================================
echo  Compilation Complete!
echo ======================================
echo.
echo Output file: phase1_clinical_trials_comprehensive.pdf
echo.

echo Cleaning up auxiliary files...
del *.aux *.log *.out *.toc 2>nul

echo Done! Opening PDF...
start phase1_clinical_trials_comprehensive.pdf

pause
'''
    
    with open('compile_comprehensive.bat', 'w') as f:
        f.write(compile_script)
    
    print("\nTo compile the comprehensive LaTeX document:")
    print("1. Install a LaTeX distribution (e.g., MiKTeX or TeX Live)")
    print("2. Run: compile_comprehensive.bat")
    print("   Or manually run: pdflatex phase1_clinical_trials_comprehensive.tex")
    print("\nThis version preserves ALL original content from the source PDF.")

if __name__ == "__main__":
    main()

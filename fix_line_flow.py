import re

def fix_line_flow_in_latex():
    """Fix line flow issues in the comprehensive LaTeX document"""
    
    # Read the current LaTeX file
    with open('phase1_comprehensive_full.tex', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split content into lines for processing
    lines = content.split('\n')
    
    # Process lines to fix flow issues
    fixed_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        
        # Skip LaTeX commands and environments
        if (line.strip().startswith('\\') or 
            line.strip().startswith('%') or 
            line.strip() == '' or
            line.strip().startswith('{') or
            line.strip().startswith('}')):
            fixed_lines.append(line)
            i += 1
            continue
        
        # Check if this is a text paragraph that needs flow fixing
        if (line.strip() and 
            not line.strip().startswith('\\') and 
            not line.strip().startswith('%')):
            
            # Start building a continuous paragraph
            paragraph = line.strip()
            i += 1
            
            # Continue collecting lines until we hit a break
            while i < len(lines):
                next_line = lines[i].strip()
                
                # Stop if we hit LaTeX commands, empty lines, or new sections
                if (not next_line or 
                    next_line.startswith('\\') or 
                    next_line.startswith('%') or
                    next_line.startswith('{') or
                    next_line.startswith('}') or
                    next_line.endswith(':') or
                    re.match(r'^\d+\.', next_line) or
                    re.match(r'^[a-z]\.', next_line) or
                    next_line.startswith('•')):
                    break
                
                # Add to paragraph with proper spacing
                if paragraph and not paragraph.endswith(' '):
                    paragraph += ' '
                paragraph += next_line
                i += 1
            
            # Add the fixed paragraph
            if paragraph:
                fixed_lines.append(paragraph)
                fixed_lines.append('')  # Add blank line after paragraph
        else:
            fixed_lines.append(line)
            i += 1
    
    # Join the fixed lines
    fixed_content = '\n'.join(fixed_lines)
    
    # Additional cleaning
    fixed_content = clean_text_flow(fixed_content)
    
    # Write the fixed content
    with open('phase1_comprehensive_full_fixed.tex', 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    return fixed_content

def clean_text_flow(content):
    """Additional cleaning for better text flow"""
    
    # Remove excessive blank lines
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    # Fix sentence flow - remove line breaks within sentences
    lines = content.split('\n')
    cleaned_lines = []
    
    for i, line in enumerate(lines):
        line = line.strip()
        
        # Skip empty lines and LaTeX commands
        if not line or line.startswith('\\') or line.startswith('%'):
            cleaned_lines.append(line)
            continue
        
        # Check if this line should be joined with the previous
        if (cleaned_lines and 
            cleaned_lines[-1] and 
            not cleaned_lines[-1].startswith('\\') and 
            not cleaned_lines[-1].endswith('.') and
            not cleaned_lines[-1].endswith(':') and
            not cleaned_lines[-1].endswith('!') and
            not cleaned_lines[-1].endswith('?') and
            not line.startswith('\\') and
            not re.match(r'^\d+\.', line) and
            not re.match(r'^[a-z]\.', line) and
            not line.startswith('•')):
            
            # Join with previous line
            cleaned_lines[-1] += ' ' + line
        else:
            cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def create_improved_compilation_script():
    """Create an improved compilation script"""
    
    compile_script = r'''
@echo off
echo ======================================
echo  Fixed Comprehensive Phase 1 LaTeX Compiler
echo ======================================
echo.

echo Checking for LaTeX installation...
where pdflatex >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: pdflatex not found. Please install a LaTeX distribution.
    echo   - MiKTeX: https://miktex.org/
    echo   - TeX Live: https://www.tug.org/texlive/
    pause
    exit /b 1
)

echo LaTeX found. Compiling fixed comprehensive document...
echo.

echo First compilation pass...
pdflatex -interaction=nonstopmode phase1_comprehensive_full_fixed.tex
if %errorlevel% neq 0 (
    echo WARNING: First compilation had issues. Continuing...
)

echo Second compilation pass (for cross-references)...
pdflatex -interaction=nonstopmode phase1_comprehensive_full_fixed.tex
if %errorlevel% neq 0 (
    echo WARNING: Second compilation had issues. Continuing...
)

echo Third compilation pass (for final formatting)...
pdflatex -interaction=nonstopmode phase1_comprehensive_full_fixed.tex

echo.
echo ======================================
echo  Compilation Complete!
echo ======================================
echo.

if exist phase1_comprehensive_full_fixed.pdf (
    echo SUCCESS: phase1_comprehensive_full_fixed.pdf has been created!
    echo.
    echo Document features:
    echo   - Fixed text flow (no awkward line breaks)
    echo   - Professional formatting
    echo   - All original content preserved
    echo   - High-quality figures included
    echo.
    echo Cleaning up auxiliary files...
    del *.aux *.log *.out *.toc *.fls *.fdb_latexmk 2>nul
    echo.
    echo Opening PDF...
    start phase1_comprehensive_full_fixed.pdf
) else (
    echo ERROR: PDF was not created. Check the log file for errors.
    echo.
    echo Troubleshooting tips:
    echo   - Ensure all figure files are present
    echo   - Check LaTeX log for missing packages
    echo   - Verify LaTeX installation is complete
)

pause
'''
    
    with open('compile_fixed.bat', 'w') as f:
        f.write(compile_script)
    
    print("Created improved compilation script: compile_fixed.bat")

def main():
    """Main function to fix line flow issues"""
    
    print("Fixing line flow issues in comprehensive LaTeX document...")
    
    # Fix the line flow
    fixed_content = fix_line_flow_in_latex()
    
    # Create improved compilation script
    create_improved_compilation_script()
    
    print(f"✅ Fixed LaTeX document created: phase1_comprehensive_full_fixed.tex")
    print(f"✅ Document length: {len(fixed_content):,} characters")
    print(f"✅ Compilation script created: compile_fixed.bat")
    
    print("\nImprovements made:")
    print("- ✅ Fixed awkward line breaks within sentences")
    print("- ✅ Improved paragraph flow")
    print("- ✅ Maintained proper LaTeX structure")
    print("- ✅ Preserved all original content")
    print("- ✅ Enhanced readability")
    
    print("\nTo compile the fixed document:")
    print("1. Run: compile_fixed.bat")
    print("2. The document will have proper text flow and professional formatting")

if __name__ == "__main__":
    main()

import os
import re
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib.patches import Rectangle, FancyBboxPatch
import matplotlib.patches as mpatches

def create_latex_document(text):
    """Convert extracted text to LaTeX format"""
    
    # Clean and format the text
    lines = text.split('\n')
    
    latex_content = r"""\documentclass[11pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{geometry}
\usepackage{graphicx}
\usepackage{float}
\usepackage{caption}
\usepackage{subcaption}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{booktabs}
\usepackage{hyperref}
\usepackage{enumitem}
\usepackage{tikz}
\usepackage{pgfplots}
\pgfplotsset{compat=1.17}

\geometry{margin=1in}
\setlength{\parindent}{0pt}
\setlength{\parskip}{0.5em}

\title{Phase 1 Clinical Trials: A Comprehensive Overview for Clinical Pharmacologists}
\author{}
\date{}

\begin{document}

\maketitle

"""
    
    # Process the text content
    current_section = ""
    in_list = False
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Skip the title and author lines as they're already in the LaTeX header
        if line in ["Phase 1 Clinical Trials: A Comprehensive Overview", "for Clinical Pharmacologists"]:
            continue
            
        # Handle section headers
        if line in ["Introduction", "Objectives of Phase 1 Trials", "Key Steps in Planning and Conducting a Phase 1 Study", 
                   "Common Phase 1 Study Designs", "Dose Selection and Escalation Methods", "Regulatory Considerations", 
                   "Statistical Analysis and Interpretation", "Limitations and Challenges", "Conclusion"]:
            if in_list:
                latex_content += "\\end{itemize}\n\n"
                in_list = False
            latex_content += f"\\section{{{line}}}\n\n"
            current_section = line
        
        # Handle subsection headers (numbered points)
        elif re.match(r'^\d+\.\s+', line):
            if in_list:
                latex_content += "\\end{itemize}\n\n"
                in_list = False
            subsection_title = re.sub(r'^\d+\.\s+', '', line).rstrip(':')
            latex_content += f"\\subsection{{{subsection_title}}}\n\n"
        
        # Handle bullet points
        elif line.startswith('•'):
            if not in_list:
                latex_content += "\\begin{itemize}\n"
                in_list = True
            item_text = line[1:].strip()
            latex_content += f"\\item {item_text}\n"
        
        # Handle figure placeholders
        elif line.startswith('Figure:'):
            if in_list:
                latex_content += "\\end{itemize}\n\n"
                in_list = False
            figure_title = line[7:].strip()
            if "Integrated Phase 1 Study Design" in figure_title:
                latex_content += create_integrated_study_design_figure()
            elif "Dose Escalation in Oncology" in figure_title:
                latex_content += create_dose_escalation_figure()
            else:
                latex_content += f"\\begin{{figure}}[H]\n\\centering\n\\includegraphics[width=0.8\\textwidth]{{placeholder.png}}\n\\caption{{{figure_title}}}\n\\end{{figure}}\n\n"
        
        # Handle figure references
        elif re.search(r'Figure \d+', line):
            if in_list:
                latex_content += "\\end{itemize}\n\n"
                in_list = False
            # Replace figure reference with proper LaTeX reference
            line = re.sub(r'Figure (\d+)', r'Figure~\\ref{fig:\\1}', line)
            latex_content += f"{line}\n\n"
            
        # Handle regular text
        else:
            if in_list:
                latex_content += "\\end{itemize}\n\n"
                in_list = False
            # Clean up the text
            line = line.replace('_', '\\_')
            line = line.replace('%', '\\%')
            line = line.replace('&', '\\&')
            line = line.replace('#', '\\#')
            latex_content += f"{line}\n\n"
    
    if in_list:
        latex_content += "\\end{itemize}\n\n"
    
    latex_content += "\\end{document}"
    
    return latex_content

def create_integrated_study_design_figure():
    """Create a figure showing integrated Phase 1 study design"""
    
    # Create the figure
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # Define boxes and their positions
    boxes = [
        {'name': 'Single Ascending\nDose (SAD)', 'pos': (1, 6), 'color': 'lightblue'},
        {'name': 'Multiple Ascending\nDose (MAD)', 'pos': (4, 6), 'color': 'lightgreen'},
        {'name': 'Food Effect\nStudy', 'pos': (7, 6), 'color': 'lightyellow'},
        {'name': 'Drug-Drug\nInteraction', 'pos': (10, 6), 'color': 'lightcoral'},
        {'name': 'Special\nPopulations', 'pos': (2.5, 3), 'color': 'lightpink'},
        {'name': 'Formulation\nComparison', 'pos': (5.5, 3), 'color': 'lightgray'},
        {'name': 'Dose Finding\nfor Phase 2', 'pos': (8.5, 3), 'color': 'lightcyan'},
        {'name': 'Phase 2\nRecommendation', 'pos': (5.5, 1), 'color': 'gold'}
    ]
    
    # Draw boxes
    for box in boxes:
        rect = FancyBboxPatch((box['pos'][0]-0.7, box['pos'][1]-0.5), 1.4, 1, 
                             boxstyle="round,pad=0.1", 
                             facecolor=box['color'], 
                             edgecolor='black', linewidth=1)
        ax.add_patch(rect)
        ax.text(box['pos'][0], box['pos'][1], box['name'], 
                ha='center', va='center', fontsize=9, weight='bold')
    
    # Draw arrows
    arrows = [
        ((1.7, 6), (3.3, 6)),      # SAD to MAD
        ((4.7, 6), (6.3, 6)),      # MAD to Food Effect
        ((7.7, 6), (9.3, 6)),      # Food Effect to DDI
        ((2.5, 5.5), (2.5, 3.5)),  # From top to Special Pop
        ((5.5, 5.5), (5.5, 3.5)),  # From middle to Formulation
        ((8.5, 5.5), (8.5, 3.5)),  # From top to Dose Finding
        ((3.2, 3), (4.8, 3)),      # Special Pop to Formulation
        ((6.2, 3), (7.8, 3)),      # Formulation to Dose Finding
        ((5.5, 2.5), (5.5, 1.5)),  # To Phase 2
    ]
    
    for start, end in arrows:
        ax.annotate('', xy=end, xytext=start,
                   arrowprops=dict(arrowstyle='->', lw=1.5, color='black'))
    
    ax.set_xlim(0, 11)
    ax.set_ylim(0, 7)
    ax.set_aspect('equal')
    ax.axis('off')
    ax.set_title('Integrated Phase 1 Study Design', fontsize=14, weight='bold', pad=20)
    
    # Save the figure
    plt.tight_layout()
    plt.savefig('integrated_phase1_design.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return r"""
\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{integrated_phase1_design.png}
\caption{Integrated Phase 1 Study Design: Modern trials often combine several Phase 1 elements adaptively. The schematic shows how a single protocol can encompass SAD, MAD, and additional evaluations.}
\label{fig:integrated_design}
\end{figure}

"""

def create_dose_escalation_figure():
    """Create a figure showing dose escalation methods"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # 3+3 Design
    ax1.set_title('3+3 Design', fontsize=12, weight='bold')
    
    # Create a flowchart for 3+3 design
    dose_levels = [1, 2, 3, 4, 5]
    y_pos = [5, 4, 3, 2, 1]
    
    for i, (dose, y) in enumerate(zip(dose_levels, y_pos)):
        # Draw dose level box
        rect = Rectangle((0.5, y-0.3), 1, 0.6, facecolor='lightblue', edgecolor='black')
        ax1.add_patch(rect)
        ax1.text(1, y, f'Dose {dose}', ha='center', va='center', fontsize=10, weight='bold')
        
        # Draw decision tree
        if i < len(dose_levels) - 1:
            # 0/3 DLT - escalate
            ax1.text(2.5, y+0.2, '0/3 DLT', ha='center', va='center', fontsize=8)
            ax1.arrow(1.5, y+0.2, 0.8, 0, head_width=0.05, head_length=0.1, fc='green', ec='green')
            
            # 1/3 DLT - add 3 more
            ax1.text(2.5, y, '1/3 DLT', ha='center', va='center', fontsize=8)
            ax1.arrow(1.5, y, 0.8, 0, head_width=0.05, head_length=0.1, fc='orange', ec='orange')
            
            # 2+/3 DLT - stop
            ax1.text(2.5, y-0.2, '2+/3 DLT', ha='center', va='center', fontsize=8)
            ax1.arrow(1.5, y-0.2, 0.8, 0, head_width=0.05, head_length=0.1, fc='red', ec='red')
    
    ax1.set_xlim(0, 4)
    ax1.set_ylim(0, 6)
    ax1.axis('off')
    
    # CRM Design
    ax2.set_title('Continual Reassessment Method (CRM)', fontsize=12, weight='bold')
    
    # Create dose-toxicity curve
    doses = np.linspace(1, 10, 100)
    toxicity_prob = 1 / (1 + np.exp(-(doses - 5)))  # Sigmoid curve
    
    ax2.plot(doses, toxicity_prob, 'b-', linewidth=2, label='Estimated Toxicity Curve')
    ax2.axhline(y=0.25, color='red', linestyle='--', label='Target DLT Rate (25%)')
    
    # Add some example data points
    dose_points = [2, 4, 6, 8]
    toxicity_points = [0.1, 0.2, 0.4, 0.7]
    patient_outcomes = ['No DLT', 'No DLT', 'DLT', 'DLT']
    
    for i, (dose, tox, outcome) in enumerate(zip(dose_points, toxicity_points, patient_outcomes)):
        color = 'green' if outcome == 'No DLT' else 'red'
        ax2.plot(dose, tox, 'o', color=color, markersize=8)
        ax2.text(dose, tox + 0.05, f'Patient {i+1}', ha='center', va='bottom', fontsize=8)
    
    ax2.set_xlabel('Dose Level')
    ax2.set_ylabel('Toxicity Probability')
    ax2.set_ylim(0, 1)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('dose_escalation_methods.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return r"""
\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{dose_escalation_methods.png}
\caption{Dose Escalation in Oncology: Comparison of (a) the rule-based "3+3" design and (b) the model-based Continual Reassessment Method (CRM). The 3+3 design uses fixed rules for dose escalation, while CRM uses a Bayesian model to estimate the dose-toxicity curve and recommend optimal doses.}
\label{fig:dose_escalation}
\end{figure}

"""

def create_pk_profile_figure():
    """Create a pharmacokinetic profile figure"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # Single dose PK
    time = np.linspace(0, 24, 100)
    doses = [10, 30, 100, 300]
    colors = ['blue', 'green', 'orange', 'red']
    
    for dose, color in zip(doses, colors):
        # Simple one-compartment model
        ka = 1.5  # absorption rate
        ke = 0.1  # elimination rate
        conc = dose * ka / (ka - ke) * (np.exp(-ke * time) - np.exp(-ka * time))
        conc[conc < 0] = 0
        ax1.plot(time, conc, color=color, linewidth=2, label=f'{dose} mg')
    
    ax1.set_xlabel('Time (hours)')
    ax1.set_ylabel('Plasma Concentration (ng/mL)')
    ax1.set_title('Single Dose Pharmacokinetics')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_yscale('log')
    
    # Multiple dose PK
    time_md = np.linspace(0, 168, 1000)  # 1 week
    dose_times = np.arange(0, 168, 24)  # Daily dosing
    
    # Simulate multiple dosing
    conc_md = np.zeros_like(time_md)
    for dose_time in dose_times:
        mask = time_md >= dose_time
        t_rel = time_md[mask] - dose_time
        dose_conc = 100 * ka / (ka - ke) * (np.exp(-ke * t_rel) - np.exp(-ka * t_rel))
        dose_conc[dose_conc < 0] = 0
        conc_md[mask] += dose_conc
    
    ax2.plot(time_md, conc_md, 'blue', linewidth=2, label='100 mg daily')
    ax2.set_xlabel('Time (hours)')
    ax2.set_ylabel('Plasma Concentration (ng/mL)')
    ax2.set_title('Multiple Dose Pharmacokinetics')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_yscale('log')
    
    plt.tight_layout()
    plt.savefig('pk_profiles.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return r"""
\begin{figure}[H]
\centering
\includegraphics[width=0.9\textwidth]{pk_profiles.png}
\caption{Pharmacokinetic Profiles: (a) Single dose concentration-time profiles showing dose proportionality across different dose levels, and (b) Multiple dose profile showing accumulation to steady-state with daily dosing.}
\label{fig:pk_profiles}
\end{figure}

"""

def create_safety_monitoring_figure():
    """Create a figure showing safety monitoring in Phase 1"""
    
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    
    # Create a timeline
    timeline = np.arange(0, 15)
    
    # Different monitoring activities
    activities = {
        'Screening': [0, 1],
        'Baseline': [2],
        'Dosing': [3],
        'Intensive Monitoring': [3, 4, 5],
        'Safety Review': [6],
        'Follow-up': [7, 8, 9, 10, 11, 12, 13, 14]
    }
    
    colors = ['red', 'orange', 'yellow', 'green', 'blue', 'purple']
    
    for i, (activity, times) in enumerate(activities.items()):
        y_pos = i * 0.5
        for time in times:
            ax.barh(y_pos, 0.8, left=time, height=0.3, 
                   color=colors[i], alpha=0.7, edgecolor='black')
        ax.text(-1, y_pos, activity, ha='right', va='center', fontsize=10)
    
    # Add vertical lines for key timepoints
    ax.axvline(x=3, color='red', linestyle='--', alpha=0.5, label='Dosing')
    ax.axvline(x=6, color='blue', linestyle='--', alpha=0.5, label='Safety Review')
    
    ax.set_xlim(-3, 15)
    ax.set_ylim(-0.5, 3)
    ax.set_xlabel('Study Day')
    ax.set_title('Phase 1 Safety Monitoring Timeline', fontsize=14, weight='bold')
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # Remove y-axis
    ax.set_yticks([])
    
    plt.tight_layout()
    plt.savefig('safety_monitoring.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return r"""
\begin{figure}[H]
\centering
\includegraphics[width=0.8\textwidth]{safety_monitoring.png}
\caption{Phase 1 Safety Monitoring Timeline: Showing the intensive monitoring schedule from screening through follow-up, with key safety review points highlighted.}
\label{fig:safety_monitoring}
\end{figure}

"""

def main():
    """Main function to convert PDF to LaTeX with figures"""
    
    # Read the extracted text
    with open('extracted_pdf_text_pymupdf.txt', 'r', encoding='utf-8') as f:
        text = f.read()
    
    # Generate figures
    print("Generating figures...")
    create_integrated_study_design_figure()
    create_dose_escalation_figure()
    create_pk_profile_figure()
    create_safety_monitoring_figure()
    
    # Create LaTeX document
    print("Converting to LaTeX...")
    latex_content = create_latex_document(text)
    
    # Add additional figures where referenced
    latex_content = latex_content.replace(
        'Figure 2 (earlier) illustrated that increasing cohort size from 4 to 6 greatly improves chances of',
        create_pk_profile_figure() + '\n\nFigure~\\ref{fig:pk_profiles} illustrated that increasing cohort size from 4 to 6 greatly improves chances of'
    )
    
    # Add safety monitoring figure
    latex_content = latex_content.replace(
        'Safety Monitoring and Data Collection:',
        create_safety_monitoring_figure() + '\n\nSafety Monitoring and Data Collection:'
    )
    
    # Write LaTeX file
    with open('phase1_clinical_trials.tex', 'w', encoding='utf-8') as f:
        f.write(latex_content)
    
    print("LaTeX file created: phase1_clinical_trials.tex")
    print("Figure files created:")
    print("- integrated_phase1_design.png")
    print("- dose_escalation_methods.png") 
    print("- pk_profiles.png")
    print("- safety_monitoring.png")
    
    # Create a compilation script
    compile_script = r'''
@echo off
echo Compiling LaTeX document...
pdflatex phase1_clinical_trials.tex
pdflatex phase1_clinical_trials.tex
echo Compilation complete. Check phase1_clinical_trials.pdf
'''
    
    with open('compile.bat', 'w') as f:
        f.write(compile_script)
    
    print("\nTo compile the LaTeX document to PDF:")
    print("1. Install a LaTeX distribution (e.g., MiKTeX or TeX Live)")
    print("2. Run: compile.bat")
    print("   Or manually run: pdflatex phase1_clinical_trials.tex")

if __name__ == "__main__":
    main()

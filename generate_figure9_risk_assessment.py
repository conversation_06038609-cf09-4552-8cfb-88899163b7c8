#!/usr/bin/env python3
"""
Generate Figure 9: Risk Assessment Matrix for Phase 1 Studies
Comprehensive risk assessment framework with categorization and mitigation strategies
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Rectangle, Circle
import numpy as np
import seaborn as sns

# Set style for professional appearance
plt.style.use('default')
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 16))

# Define colors for risk levels
colors = {
    'low': '#27AE60',      # Green
    'medium': '#F39C12',   # Orange
    'high': '#E74C3C',     # Red
    'critical': '#8E44AD', # Purple
    'background': '#ECF0F1',
    'text': '#2C3E50',
    'border': '#34495E'
}

# Main title
fig.suptitle('Risk Assessment Matrix for Phase 1 Clinical Studies',
             fontsize=20, fontweight='bold', y=0.95)

fig.text(0.5, 0.92, 'Comprehensive framework for identifying, categorizing, and mitigating risks in first-in-human studies',
         ha='center', fontsize=12, style='italic')

# Subplot 1: Risk Probability vs Impact Matrix (top left)
ax1.set_xlim(0, 5)
ax1.set_ylim(0, 5)
ax1.set_xlabel('Probability of Occurrence', fontsize=12, fontweight='bold')
ax1.set_ylabel('Impact Severity', fontsize=12, fontweight='bold')
ax1.set_title('A. Risk Probability vs Impact Matrix', fontsize=14, fontweight='bold', pad=20)

# Create risk matrix grid
for i in range(6):
    ax1.axhline(y=i, color='gray', linewidth=1, alpha=0.5)
    ax1.axvline(x=i, color='gray', linewidth=1, alpha=0.5)

# Define risk zones
risk_zones = [
    # (x, y, width, height, color, label)
    (0, 0, 2, 2, colors['low'], 'Low Risk'),
    (2, 0, 2, 2, colors['medium'], 'Medium Risk'),
    (4, 0, 1, 2, colors['high'], 'High Risk'),
    (0, 2, 2, 2, colors['medium'], 'Medium Risk'),
    (2, 2, 2, 2, colors['high'], 'High Risk'),
    (4, 2, 1, 2, colors['critical'], 'Critical Risk'),
    (0, 4, 2, 1, colors['high'], 'High Risk'),
    (2, 4, 2, 1, colors['critical'], 'Critical Risk'),
    (4, 4, 1, 1, colors['critical'], 'Critical Risk')
]

for x, y, w, h, color, label in risk_zones:
    rect = Rectangle((x, y), w, h, facecolor=color, alpha=0.3, edgecolor='black')
    ax1.add_patch(rect)

# Add risk examples as points
risk_examples = [
    (1, 1, 'Minor AE', colors['low']),
    (2.5, 1.5, 'Lab Abnormality', colors['medium']),
    (3.5, 2.5, 'Serious AE', colors['high']),
    (4.5, 4.5, 'Life-threatening Event', colors['critical']),
    (1.5, 3, 'Protocol Deviation', colors['medium']),
    (3, 3.5, 'Drug Interaction', colors['high']),
    (2, 4.2, 'Regulatory Hold', colors['critical'])
]

for x, y, label, color in risk_examples:
    circle = Circle((x, y), 0.15, color=color, alpha=0.8)
    ax1.add_patch(circle)
    ax1.text(x, y-0.4, label, ha='center', va='center', fontsize=8, fontweight='bold')

# Add axis labels
ax1.set_xticks([0.5, 1.5, 2.5, 3.5, 4.5])
ax1.set_xticklabels(['Very Low', 'Low', 'Medium', 'High', 'Very High'])
ax1.set_yticks([0.5, 1.5, 2.5, 3.5, 4.5])
ax1.set_yticklabels(['Minimal', 'Minor', 'Moderate', 'Major', 'Catastrophic'])

# Subplot 2: Risk Categories (top right)
ax2.axis('off')
ax2.set_title('B. Risk Categories and Examples', fontsize=14, fontweight='bold', pad=20)

# Create risk category boxes
def create_risk_box(ax, x, y, width, height, title, items, color):
    box = FancyBboxPatch((x, y), width, height,
                         boxstyle="round,pad=0.05",
                         facecolor=color,
                         edgecolor=colors['border'],
                         linewidth=2,
                         alpha=0.8)
    ax.add_patch(box)

    # Title
    ax.text(x + width/2, y + height - 0.1, title, ha='center', va='center',
            fontsize=11, fontweight='bold', color='white')

    # Items
    for i, item in enumerate(items):
        ax.text(x + 0.05, y + height - 0.3 - i*0.15, f'• {item}', ha='left', va='center',
                fontsize=9, color='white', fontweight='bold')

# Risk categories
categories = [
    (0.1, 3.5, 1.8, 1.4, 'Safety Risks', [
        'Unexpected toxicity',
        'Drug interactions',
        'Allergic reactions',
        'Organ dysfunction',
        'Death'
    ], colors['critical']),

    (2.1, 3.5, 1.8, 1.4, 'Regulatory Risks', [
        'IND hold',
        'Protocol violations',
        'GCP non-compliance',
        'Inspection findings',
        'Approval delays'
    ], colors['high']),

    (0.1, 1.8, 1.8, 1.4, 'Operational Risks', [
        'Recruitment delays',
        'Site performance',
        'Data quality',
        'Supply chain',
        'Staff turnover'
    ], colors['medium']),

    (2.1, 1.8, 1.8, 1.4, 'Financial Risks', [
        'Budget overruns',
        'Timeline delays',
        'Resource allocation',
        'Vendor issues',
        'Insurance claims'
    ], colors['medium']),

    (0.1, 0.1, 1.8, 1.4, 'Scientific Risks', [
        'Poor PK properties',
        'Lack of efficacy',
        'Biomarker failure',
        'Dose selection',
        'Study design flaws'
    ], colors['low']),

    (2.1, 0.1, 1.8, 1.4, 'External Risks', [
        'Competitive landscape',
        'Regulatory changes',
        'Market conditions',
        'Technology issues',
        'Force majeure'
    ], colors['low'])
]

for x, y, w, h, title, items, color in categories:
    create_risk_box(ax2, x, y, w, h, title, items, color)

ax2.set_xlim(0, 4)
ax2.set_ylim(0, 5)

# Subplot 3: Risk Mitigation Strategies (bottom left)
ax3.axis('off')
ax3.set_title('C. Risk Mitigation Strategies', fontsize=14, fontweight='bold', pad=20)

# Mitigation strategy flowchart
def create_strategy_box(ax, x, y, width, height, text, color, text_size=10):
    box = FancyBboxPatch((x, y), width, height,
                         boxstyle="round,pad=0.05",
                         facecolor=color,
                         edgecolor=colors['border'],
                         linewidth=2)
    ax.add_patch(box)
    ax.text(x + width/2, y + height/2, text, ha='center', va='center',
            fontsize=text_size, fontweight='bold', wrap=True)

# Risk mitigation flow
create_strategy_box(ax3, 1, 4, 2, 0.6, 'Risk Identification\n& Assessment', colors['background'], 9)
create_strategy_box(ax3, 0.2, 3, 1.6, 0.6, 'Prevention\nStrategies', colors['low'], 9)
create_strategy_box(ax3, 2.2, 3, 1.6, 0.6, 'Mitigation\nPlans', colors['medium'], 9)
create_strategy_box(ax3, 0.2, 2, 1.6, 0.6, 'Contingency\nPlans', colors['high'], 9)
create_strategy_box(ax3, 2.2, 2, 1.6, 0.6, 'Crisis\nManagement', colors['critical'], 9)
create_strategy_box(ax3, 1, 1, 2, 0.6, 'Continuous Monitoring\n& Review', colors['background'], 9)

# Add arrows
arrow_props = dict(arrowstyle='->', lw=2, color=colors['border'])
ax3.annotate('', xy=(1, 3.6), xytext=(1.5, 4), arrowprops=arrow_props)
ax3.annotate('', xy=(3, 3.6), xytext=(2.5, 4), arrowprops=arrow_props)
ax3.annotate('', xy=(1, 2.6), xytext=(1.5, 3), arrowprops=arrow_props)
ax3.annotate('', xy=(3, 2.6), xytext=(2.5, 3), arrowprops=arrow_props)
ax3.annotate('', xy=(1.5, 1.6), xytext=(1, 2), arrowprops=arrow_props)
ax3.annotate('', xy=(2.5, 1.6), xytext=(3, 2), arrowprops=arrow_props)

# Add strategy details
strategies = [
    (0.1, 0.5, 'Prevention: Robust protocol design, thorough preclinical data, expert team'),
    (0.1, 0.3, 'Mitigation: Safety monitoring, dose modification, early stopping rules'),
    (0.1, 0.1, 'Contingency: Emergency procedures, backup plans, alternative approaches')
]

for x, y, text in strategies:
    ax3.text(x, y, text, fontsize=9, va='center', color=colors['text'])

ax3.set_xlim(0, 4)
ax3.set_ylim(0, 5)

# Subplot 4: Risk Monitoring Dashboard (bottom right)
ax4.set_title('D. Risk Monitoring Dashboard', fontsize=14, fontweight='bold', pad=20)

# Create risk level indicators
risk_levels = ['Low', 'Medium', 'High', 'Critical']
risk_counts = [12, 8, 4, 1]  # Example counts
risk_colors = [colors['low'], colors['medium'], colors['high'], colors['critical']]

bars = ax4.bar(risk_levels, risk_counts, color=risk_colors, alpha=0.8, edgecolor='black')

# Add value labels on bars
for bar, count in zip(bars, risk_counts):
    height = bar.get_height()
    ax4.text(bar.get_x() + bar.get_width()/2., height + 0.1,
             f'{count}', ha='center', va='bottom', fontweight='bold')

ax4.set_ylabel('Number of Risks', fontsize=12, fontweight='bold')
ax4.set_xlabel('Risk Level', fontsize=12, fontweight='bold')
ax4.grid(True, alpha=0.3)
ax4.set_ylim(0, max(risk_counts) + 2)

# Add trend indicators
trend_y = max(risk_counts) * 0.8
trend_symbols = ['↓', '→', '↑', '↑']
for i, (level, symbol) in enumerate(zip(risk_levels, trend_symbols)):
    color = 'green' if symbol == '↓' else 'red' if symbol == '↑' else 'gray'
    ax4.text(i, trend_y, symbol, ha='center', va='center',
             fontsize=20, color=color, fontweight='bold')

# Add legend for trends
ax4.text(0.02, 0.98, 'Trend: ↓ Decreasing  → Stable  ↑ Increasing',
         transform=ax4.transAxes, fontsize=9, va='top',
         bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

plt.tight_layout()
plt.subplots_adjust(top=0.88)
plt.savefig('figure9_risk_assessment.png', dpi=300, bbox_inches='tight',
            facecolor='white', edgecolor='none')
plt.close()

print("Figure 9 (Risk Assessment Matrix) generated successfully!")
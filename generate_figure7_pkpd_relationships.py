#!/usr/bin/env python3
"""
Generate Figure 7: PK/PD Relationships in Phase 1 Studies
Comprehensive visualization of pharmacokinetic-pharmacodynamic relationships
"""

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib.patches import FancyBboxPatch
import matplotlib.patches as patches

# Set style for professional appearance
plt.style.use('default')
sns.set_palette("husl")

# Create figure with subplots
fig = plt.figure(figsize=(18, 14))

# Define colors
colors = {
    'pk': '#3498DB',
    'pd': '#E74C3C',
    'pkpd': '#9B59B6',
    'safety': '#F39C12',
    'efficacy': '#27AE60',
    'background': '#ECF0F1',
    'text': '#2C3E50'
}

# Main title with better spacing
fig.suptitle('Pharmacokinetic-Pharmacodynamic Relationships in Phase 1 Studies',
             fontsize=18, fontweight='bold', y=0.96)

# Subtitle
fig.text(0.5, 0.93, 'Integrated analysis of drug exposure, response, and safety relationships for optimal dose selection',
         ha='center', fontsize=12, style='italic')

# Subplot 1: PK Profile (top left)
ax1 = plt.subplot(2, 3, 1)
time = np.linspace(0, 24, 100)
conc_low = 10 * np.exp(-0.1 * time)
conc_med = 25 * np.exp(-0.08 * time)
conc_high = 50 * np.exp(-0.06 * time)

ax1.plot(time, conc_low, 'b-', linewidth=3, label='Low Dose (10 mg)', alpha=0.8)
ax1.plot(time, conc_med, 'g-', linewidth=3, label='Medium Dose (25 mg)', alpha=0.8)
ax1.plot(time, conc_high, 'r-', linewidth=3, label='High Dose (50 mg)', alpha=0.8)

ax1.axhline(y=5, color='orange', linestyle='--', linewidth=2, alpha=0.7, label='Min. Effective Conc.')
ax1.axhline(y=40, color='red', linestyle='--', linewidth=2, alpha=0.7, label='Toxicity Threshold')

ax1.set_xlabel('Time (hours)', fontsize=11, fontweight='bold')
ax1.set_ylabel('Plasma Concentration (ng/mL)', fontsize=11, fontweight='bold')
ax1.set_title('A. Pharmacokinetic Profiles', fontsize=13, fontweight='bold', pad=15)
ax1.legend(fontsize=9, loc='upper right')
ax1.grid(True, alpha=0.3)
ax1.set_xlim(0, 24)
ax1.set_ylim(0, 60)

# Subplot 2: Dose-Response Relationship (top middle)
ax2 = plt.subplot(2, 3, 2)
doses = np.array([1, 5, 10, 25, 50, 100, 200])
response = 100 * doses / (doses + 20)  # Emax model
safety_score = np.array([95, 90, 85, 75, 60, 40, 20])

line1 = ax2.plot(doses, response, 'bo-', linewidth=3, markersize=8, label='Efficacy Response', alpha=0.8)
ax2_twin = ax2.twinx()
line2 = ax2_twin.plot(doses, safety_score, 'ro-', linewidth=3, markersize=8, label='Safety Score', alpha=0.8)

ax2.axvline(x=25, color='green', linestyle='--', linewidth=2, alpha=0.7, label='Optimal Dose')
ax2.set_xlabel('Dose (mg)', fontsize=11, fontweight='bold')
ax2.set_ylabel('Efficacy Response (%)', fontsize=11, fontweight='bold', color='blue')
ax2_twin.set_ylabel('Safety Score (%)', fontsize=11, fontweight='bold', color='red')
ax2.set_title('B. Dose-Response Relationships', fontsize=13, fontweight='bold', pad=15)
ax2.set_xscale('log')
ax2.grid(True, alpha=0.3)

# Combined legend
lines1, labels1 = ax2.get_legend_handles_labels()
lines2, labels2 = ax2_twin.get_legend_handles_labels()
ax2.legend(lines1 + lines2, labels1 + labels2, loc='center right', fontsize=9)

# Subplot 3: PK/PD Correlation (top right)
ax3 = plt.subplot(2, 3, 3)
concentrations = np.linspace(0, 60, 50)
pd_response = 80 * concentrations / (concentrations + 15)

ax3.plot(concentrations, pd_response, 'purple', linewidth=4, alpha=0.8, label='PK/PD Model')
ax3.scatter([5, 15, 30, 45], [20, 40, 60, 70], s=100, c=['blue', 'green', 'orange', 'red'],
           alpha=0.8, edgecolors='black', linewidth=2, label='Observed Data')

ax3.axvline(x=5, color='orange', linestyle='--', alpha=0.7, label='Min. Effective Conc.')
ax3.axvline(x=40, color='red', linestyle='--', alpha=0.7, label='Toxicity Threshold')
ax3.axhspan(20, 60, alpha=0.2, color='green', label='Therapeutic Window')

ax3.set_xlabel('Plasma Concentration (ng/mL)', fontsize=11, fontweight='bold')
ax3.set_ylabel('Pharmacodynamic Response (%)', fontsize=11, fontweight='bold')
ax3.set_title('C. PK/PD Correlation', fontsize=13, fontweight='bold', pad=15)
ax3.legend(fontsize=9, loc='lower right')
ax3.grid(True, alpha=0.3)
ax3.set_xlim(0, 60)
ax3.set_ylim(0, 100)

# Subplot 4: Time-Response Profile (bottom left)
ax4 = plt.subplot(2, 3, 4)
time_pd = np.linspace(0, 24, 100)
biomarker_low = 20 + 15 * np.exp(-0.15 * time_pd) * np.sin(0.5 * time_pd)
biomarker_med = 20 + 30 * np.exp(-0.12 * time_pd) * np.sin(0.5 * time_pd)
biomarker_high = 20 + 45 * np.exp(-0.1 * time_pd) * np.sin(0.5 * time_pd)

ax4.plot(time_pd, biomarker_low, 'b-', linewidth=3, label='Low Dose', alpha=0.8)
ax4.plot(time_pd, biomarker_med, 'g-', linewidth=3, label='Medium Dose', alpha=0.8)
ax4.plot(time_pd, biomarker_high, 'r-', linewidth=3, label='High Dose', alpha=0.8)
ax4.axhline(y=20, color='black', linestyle='-', alpha=0.5, label='Baseline')

ax4.set_xlabel('Time (hours)', fontsize=11, fontweight='bold')
ax4.set_ylabel('Biomarker Response (% change)', fontsize=11, fontweight='bold')
ax4.set_title('D. Time-Response Profiles', fontsize=13, fontweight='bold', pad=15)
ax4.legend(fontsize=9)
ax4.grid(True, alpha=0.3)
ax4.set_xlim(0, 24)

# Subplot 5: Safety Window Analysis (bottom middle)
ax5 = plt.subplot(2, 3, 5)
dose_range = np.linspace(1, 200, 100)
therapeutic_index = 100 * np.exp(-((dose_range - 25) / 30) ** 2)
safety_margin = 80 * np.exp(-((dose_range - 15) / 25) ** 2)

ax5.fill_between(dose_range, 0, therapeutic_index, alpha=0.3, color='green', label='Therapeutic Index')
ax5.fill_between(dose_range, 0, safety_margin, alpha=0.3, color='orange', label='Safety Margin')
ax5.plot(dose_range, therapeutic_index, 'g-', linewidth=3, alpha=0.8)
ax5.plot(dose_range, safety_margin, 'orange', linewidth=3, alpha=0.8)

ax5.axvline(x=25, color='blue', linestyle='--', linewidth=2, alpha=0.7, label='Recommended Dose')
ax5.set_xlabel('Dose (mg)', fontsize=11, fontweight='bold')
ax5.set_ylabel('Index Score', fontsize=11, fontweight='bold')
ax5.set_title('E. Therapeutic Window Analysis', fontsize=13, fontweight='bold', pad=15)
ax5.legend(fontsize=9)
ax5.grid(True, alpha=0.3)
ax5.set_xlim(0, 100)

# Subplot 6: PK/PD Modeling Framework (bottom right)
ax6 = plt.subplot(2, 3, 6)
ax6.axis('off')

# Create boxes for modeling framework
def create_model_box(ax, x, y, width, height, text, color, fontsize=9):
    box = FancyBboxPatch((x, y), width, height,
                         boxstyle="round,pad=0.02",
                         facecolor=color,
                         edgecolor='black',
                         linewidth=1.5)
    ax.add_patch(box)
    ax.text(x + width/2, y + height/2, text, ha='center', va='center',
            fontsize=fontsize, fontweight='bold', wrap=True)

# PK/PD modeling framework with better spacing
create_model_box(ax6, 0.1, 0.85, 0.8, 0.12, 'PK/PD Modeling Framework', colors['pkpd'], 11)

create_model_box(ax6, 0.05, 0.68, 0.4, 0.14, 'PK Component:\n• Absorption\n• Distribution\n• Metabolism\n• Elimination', colors['pk'], 8)

create_model_box(ax6, 0.55, 0.68, 0.4, 0.14, 'PD Component:\n• Target Binding\n• Signal Transduction\n• Response\n• Tolerance', colors['pd'], 8)

create_model_box(ax6, 0.05, 0.48, 0.4, 0.14, 'Population PK:\n• Covariates\n• Variability\n• Allometry\n• Special Populations', colors['efficacy'], 8)

create_model_box(ax6, 0.55, 0.48, 0.4, 0.14, 'Exposure-Response:\n• Efficacy\n• Safety\n• Biomarkers\n• Time Course', colors['safety'], 8)

create_model_box(ax6, 0.3, 0.28, 0.4, 0.14, 'Model Applications:\n• Dose Selection\n• Schedule Optimization\n• Go/No-Go Decisions\n• Phase 2 Design', colors['background'], 8)

# Add arrows with better positioning
arrow_props = dict(arrowstyle='->', lw=2, color='black')
ax6.annotate('', xy=(0.25, 0.68), xytext=(0.25, 0.85), arrowprops=arrow_props)
ax6.annotate('', xy=(0.75, 0.68), xytext=(0.75, 0.85), arrowprops=arrow_props)
ax6.annotate('', xy=(0.5, 0.42), xytext=(0.25, 0.48), arrowprops=arrow_props)
ax6.annotate('', xy=(0.5, 0.42), xytext=(0.75, 0.48), arrowprops=arrow_props)

ax6.set_xlim(0, 1)
ax6.set_ylim(0, 1)
ax6.set_title('F. PK/PD Modeling Framework', fontsize=13, fontweight='bold', pad=15)

plt.tight_layout()
plt.subplots_adjust(top=0.90)  # Adjust for title spacing
plt.savefig('figure7_pkpd_relationships.png', dpi=300, bbox_inches='tight',
            facecolor='white', edgecolor='none')
plt.close()

print("Figure 7 (PK/PD Relationships) generated successfully!")
#!/usr/bin/env python3
"""
Generate Figure 4: Statistical Models and Methods in Phase 1 Clinical Trials
Creates a high-quality PNG figure to replace the TikZ version
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np

# Set up the figure with high DPI for LaTeX
plt.rcParams.update({
    'font.size': 10,
    'font.family': 'serif',
    'font.serif': ['Times New Roman'],
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'text.usetex': False,  # Avoid LaTeX dependency issues
    'axes.linewidth': 1.2,
    'patch.linewidth': 1.2
})

# Create figure and axis
fig, ax = plt.subplots(1, 1, figsize=(16, 12))
ax.set_xlim(0, 15)
ax.set_ylim(0, 12)
ax.axis('off')

# Title
ax.text(7.5, 11.5, 'Statistical Models and Methods in Phase 1 Clinical Trials', 
        fontsize=18, fontweight='bold', ha='center', va='center')

# Define colors
colors = {
    'blue': '#ADD8E6',
    'green': '#90EE90', 
    'green_dark': '#7FDD7F',
    'orange': '#FFD700',
    'orange_dark': '#FFC700',
    'red': '#FFB6C1',
    'purple': '#DDA0DD',
    'purple_dark': '#D090D0',
    'yellow': '#FFFFE0',
    'gray': '#F0F0F0',
    'cyan': '#E0FFFF'
}

# Helper function to create rounded rectangles
def create_box(ax, x, y, width, height, text, color, fontsize=10):
    # Create rounded rectangle
    box = FancyBboxPatch((x-width/2, y-height/2), width, height,
                         boxstyle="round,pad=0.1", 
                         facecolor=color, edgecolor='black', linewidth=1.5)
    ax.add_patch(box)
    
    # Add text
    ax.text(x, y, text, ha='center', va='center', fontsize=fontsize, 
            fontweight='bold' if 'bold' in text else 'normal',
            wrap=True)

# Primary Analysis Categories (Left Column)
create_box(ax, 2.5, 9.5, 4.5, 1.5, 
           'Descriptive Statistics\n• Safety data summaries\n• AE frequency tables\n• PK parameter means\n• 90% confidence intervals\n• Geometric means & CV%',
           colors['blue'], 9)

create_box(ax, 2.5, 7.5, 4.5, 1.5,
           'Non-Compartmental Analysis\n• C_max, AUC, t_1/2\n• Clearance (CL/F)\n• Volume of distribution\n• Bioavailability (F)\n• Accumulation ratios',
           colors['green'], 9)

create_box(ax, 2.5, 5.5, 4.5, 1.5,
           'Population PK Modeling\n• Mixed-effects models\n• Covariate relationships\n• Inter-individual variability\n• NONMEM/Monolix\n• Model diagnostics',
           colors['green_dark'], 9)

# Dose-Response Models (Middle Column)
create_box(ax, 7.5, 9.5, 4.5, 1.5,
           'Dose Proportionality\n• Power model analysis\n• log(AUC) vs log(dose)\n• ANOVA on dose-normalized\n• 90% CI for slope\n• FDA/EMA guidelines',
           colors['orange'], 9)

create_box(ax, 7.5, 7.5, 4.5, 1.5,
           'Exposure-Response Models\n• E_max models\n• Linear/log-linear\n• Sigmoid E_max\n• Hill equation\n• Threshold models',
           colors['orange_dark'], 9)

create_box(ax, 7.5, 5.5, 4.5, 1.5,
           'Safety Analysis Models\n• Logistic regression (DLT)\n• Time-to-event analysis\n• Cochran-Armitage trend\n• Shift table analysis\n• CTCAE grading',
           colors['red'], 9)

# Adaptive Models (Right Column)
create_box(ax, 12.5, 9.5, 4.5, 1.5,
           'Model-Based Dose Escalation\n• Continual Reassessment\n• Bayesian logistic regression\n• EWOC designs\n• BOIN method\n• Real-time updates',
           colors['purple'], 9)

create_box(ax, 12.5, 7.5, 4.5, 1.5,
           'Adaptive Trial Designs\n• Sequential monitoring\n• Interim analyses\n• Sample size re-estimation\n• Seamless Phase 1/2\n• Platform trials',
           colors['purple_dark'], 9)

create_box(ax, 12.5, 5.5, 4.5, 1.5,
           'Specialized Analyses\n• QTc analysis (ICH E14)\n• Food effect (BE studies)\n• DDI assessment\n• Bioequivalence (FDA)\n• Immunogenicity (ADA)',
           colors['yellow'], 9)

# Model Selection Criteria (Center Bottom)
create_box(ax, 7.5, 3, 7, 1.8,
           'Model Selection Considerations:\n• Sample size constraints (N = 20-100 typical)\n• Exploratory vs. confirmatory objectives\n• Regulatory requirements (ICH, FDA, EMA)\n• Data characteristics (continuous, categorical, time-to-event)\n• Missing data handling strategies\n• Real-time decision making requirements\n• Software validation and 21 CFR Part 11 compliance',
           colors['gray'], 9)

# Software boxes (Bottom Row)
software_y = 0.8
create_box(ax, 1.5, software_y, 3, 1.2,
           'SAS\n• PROC MIXED\n• PROC NLIN\n• PROC LOGISTIC\n• PROC LIFETEST',
           colors['cyan'], 8)

create_box(ax, 5, software_y, 3, 1.2,
           'R/RStudio\n• nlme, lme4\n• ggplot2, lattice\n• survival package\n• CRAN validation',
           colors['cyan'], 8)

create_box(ax, 8.5, software_y, 3, 1.2,
           'Phoenix WinNonlin\n• NCA analysis\n• Population PK\n• Bioequivalence\n• 21 CFR Part 11',
           colors['cyan'], 8)

create_box(ax, 12, software_y, 3, 1.2,
           'NONMEM\n• Population models\n• Covariate analysis\n• Simulation\n• Industry standard',
           colors['cyan'], 8)

# Add arrows to show relationships
arrow_props = dict(arrowstyle='->', lw=2, color='blue')

# Vertical arrows (left column)
ax.annotate('', xy=(2.5, 6.7), xytext=(2.5, 8.3), arrowprops=arrow_props)
ax.annotate('', xy=(2.5, 4.7), xytext=(2.5, 6.3), arrowprops=arrow_props)

# Horizontal arrows to middle column
ax.annotate('', xy=(5.2, 9.5), xytext=(4.8, 9.5), arrowprops=dict(arrowstyle='->', lw=2, color='orange'))

# Vertical arrows (middle column)
ax.annotate('', xy=(7.5, 6.7), xytext=(7.5, 8.3), arrowprops=dict(arrowstyle='->', lw=2, color='orange'))
ax.annotate('', xy=(7.5, 4.7), xytext=(7.5, 6.3), arrowprops=dict(arrowstyle='->', lw=2, color='red'))

# Horizontal arrows to right column
ax.annotate('', xy=(10.2, 9.5), xytext=(9.8, 9.5), arrowprops=dict(arrowstyle='->', lw=2, color='purple'))

# Vertical arrows (right column)
ax.annotate('', xy=(12.5, 6.7), xytext=(12.5, 8.3), arrowprops=dict(arrowstyle='->', lw=2, color='purple'))
ax.annotate('', xy=(12.5, 4.7), xytext=(12.5, 6.3), arrowprops=dict(arrowstyle='->', lw=2, color='purple'))

# Dashed arrows to criteria
ax.annotate('', xy=(6, 3.8), xytext=(3.5, 4.8), arrowprops=dict(arrowstyle='->', lw=1.5, color='gray', linestyle='--'))
ax.annotate('', xy=(7.5, 3.8), xytext=(7.5, 4.8), arrowprops=dict(arrowstyle='->', lw=1.5, color='gray', linestyle='--'))
ax.annotate('', xy=(9, 3.8), xytext=(11.5, 4.8), arrowprops=dict(arrowstyle='->', lw=1.5, color='gray', linestyle='--'))

# Dotted arrows to software
ax.annotate('', xy=(1.5, 1.4), xytext=(5, 2.2), arrowprops=dict(arrowstyle='->', lw=1, color='gray', linestyle=':'))
ax.annotate('', xy=(5, 1.4), xytext=(7, 2.2), arrowprops=dict(arrowstyle='->', lw=1, color='gray', linestyle=':'))
ax.annotate('', xy=(8.5, 1.4), xytext=(8, 2.2), arrowprops=dict(arrowstyle='->', lw=1, color='gray', linestyle=':'))
ax.annotate('', xy=(12, 1.4), xytext=(10, 2.2), arrowprops=dict(arrowstyle='->', lw=1, color='gray', linestyle=':'))

plt.tight_layout()
plt.savefig('figure4_statistical_models.png', dpi=300, bbox_inches='tight', 
            facecolor='white', edgecolor='none')
plt.close()

print("Figure 4 (Statistical Models) generated successfully as figure4_statistical_models.png")

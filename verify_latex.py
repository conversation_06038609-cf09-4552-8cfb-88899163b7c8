import os
import re

def verify_latex_document():
    """Verify that the LaTeX document is properly formatted and all figures are referenced"""
    
    # Check if all required files exist
    required_files = [
        'phase1_clinical_trials_improved.tex',
        'integrated_phase1_design.png',
        'dose_escalation_methods.png',
        'pk_profiles.png',
        'safety_monitoring.png'
    ]
    
    print("=== File Verification ===")
    all_files_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file} - Found")
        else:
            print(f"✗ {file} - Missing")
            all_files_exist = False
    
    if not all_files_exist:
        print("\nError: Some required files are missing!")
        return False
    
    # Read the LaTeX file
    with open('phase1_clinical_trials_improved.tex', 'r', encoding='utf-8') as f:
        latex_content = f.read()
    
    # Check for figure inclusions
    print("\n=== Figure Reference Verification ===")
    figure_files = [
        'integrated_phase1_design.png',
        'dose_escalation_methods.png',
        'pk_profiles.png',
        'safety_monitoring.png'
    ]
    
    figures_referenced = True
    for fig_file in figure_files:
        if fig_file in latex_content:
            print(f"✓ {fig_file} - Referenced in LaTeX")
        else:
            print(f"✗ {fig_file} - Not referenced in LaTeX")
            figures_referenced = False
    
    # Check for proper LaTeX structure
    print("\n=== LaTeX Structure Verification ===")
    required_elements = [
        (r'\\documentclass', 'Document class'),
        (r'\\usepackage\{graphicx\}', 'Graphics package'),
        (r'\\begin\{document\}', 'Document beginning'),
        (r'\\end\{document\}', 'Document ending'),
        (r'\\section\{', 'Sections'),
        (r'\\includegraphics', 'Figure inclusions'),
        (r'\\caption\{', 'Figure captions'),
        (r'\\label\{', 'Figure labels')
    ]
    
    structure_ok = True
    for pattern, description in required_elements:
        if re.search(pattern, latex_content):
            print(f"✓ {description} - Found")
        else:
            print(f"✗ {description} - Missing")
            structure_ok = False
    
    # Count sections and figures
    print("\n=== Content Statistics ===")
    section_count = len(re.findall(r'\\section\{', latex_content))
    subsection_count = len(re.findall(r'\\subsection\{', latex_content))
    figure_count = len(re.findall(r'\\includegraphics', latex_content))
    
    print(f"Sections: {section_count}")
    print(f"Subsections: {subsection_count}")
    print(f"Figures: {figure_count}")
    
    # Overall verification
    print("\n=== Overall Verification ===")
    if all_files_exist and figures_referenced and structure_ok:
        print("✓ All verifications passed!")
        print("The LaTeX document is ready for compilation.")
        return True
    else:
        print("✗ Some verifications failed!")
        print("Please check the issues above before compiling.")
        return False

if __name__ == "__main__":
    verify_latex_document()

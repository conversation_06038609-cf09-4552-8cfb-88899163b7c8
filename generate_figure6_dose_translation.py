#!/usr/bin/env python3
"""
Generate Figure 6: Dose Translation: From Preclinical to First-in-Human
Creates a high-quality PNG figure to replace the TikZ version
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch, Polygon
import numpy as np

# Set up the figure with high DPI for LaTeX
plt.rcParams.update({
    'font.size': 10,
    'font.family': 'serif',
    'font.serif': ['Times New Roman'],
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'text.usetex': False,
    'axes.linewidth': 1.2,
    'patch.linewidth': 1.2
})

# Create figure and axis with improved spacing
fig, ax = plt.subplots(1, 1, figsize=(16, 14))
ax.set_xlim(0, 15)
ax.set_ylim(0, 14)
ax.axis('off')

# Title with better positioning and spacing
ax.text(7.5, 13.3, 'Dose Translation: From Preclinical to First-in-Human Starting Dose',
        fontsize=16, fontweight='bold', ha='center', va='center')

# Subtitle for clarity
ax.text(7.5, 12.7, 'Systematic approaches for determining Phase 1 starting doses, contrasting traditional NOAEL-based\nmethods with modern mechanistic approaches',
        fontsize=11, ha='center', va='center', style='italic')

# Define colors
colors = {
    'blue': '#ADD8E6',
    'yellow': '#FFFFE0',
    'orange': '#FFD700',
    'green': '#90EE90',
    'purple': '#DDA0DD',
    'purple_dark': '#D090D0',
    'cyan': '#E0FFFF',
    'red': '#FFB6C1',
    'gray': '#F0F0F0',
    'teal': '#AFEEEE'
}

# Helper function to create rounded rectangles
def create_box(ax, x, y, width, height, text, color, fontsize=10):
    box = FancyBboxPatch((x-width/2, y-height/2), width, height,
                         boxstyle="round,pad=0.1", 
                         facecolor=color, edgecolor='black', linewidth=1.5)
    ax.add_patch(box)
    ax.text(x, y, text, ha='center', va='center', fontsize=fontsize, 
            fontweight='bold' if text.startswith('**') else 'normal',
            wrap=True)

# Helper function to create diamond shape
def create_diamond(ax, x, y, width, height, text, color, fontsize=10):
    diamond = Polygon([(x, y+height/2), (x+width/2, y), (x, y-height/2), (x-width/2, y)],
                     facecolor=color, edgecolor='black', linewidth=1.5)
    ax.add_patch(diamond)
    ax.text(x, y, text, ha='center', va='center', fontsize=fontsize, 
            fontweight='bold', wrap=True)

# Pathway labels with adjusted positioning
create_box(ax, 1, 8.5, 2.5, 1, '**Traditional\nNOAEL-Based\nPathway**', colors['blue'], 10)
create_box(ax, 13.5, 8.5, 2.5, 1, '**Mechanistic\nRisk-Based\nPathway**', colors['purple'], 10)

# Traditional NOAEL-based approach (left pathway)
create_box(ax, 3, 11, 3.5, 1.5,
           '**Preclinical Studies**\n• GLP toxicology\n• NOAEL determination\n• Most sensitive species\n• Example: 50 mg/kg (rat)',
           colors['blue'], 9)

create_box(ax, 3, 9, 3.5, 1.5,
           '**Allometric Scaling**\n• Body surface area\n• HED calculation\n• Species conversion\n• HED = 8 mg/kg (human)',
           colors['yellow'], 9)

create_box(ax, 3, 7, 3.5, 1.5,
           '**Safety Factor Application**\n• 10-fold safety margin\n• FDA guidance (2005)\n• MRSD calculation\n• MRSD = 0.8 mg/kg',
           colors['orange'], 9)

create_box(ax, 3, 5, 3.5, 1.5,
           '**Traditional Starting Dose**\n• Absolute dose: 48 mg\n• For 60 kg human\n• Conservative approach\n• Regulatory acceptance',
           colors['green'], 9)

# Modern mechanistic approaches (right pathway)
create_box(ax, 9, 10, 4, 1.5,
           '**MABEL Approach**\n• Minimum anticipated biological effect\n• In vitro human cell data\n• Receptor occupancy models\n• High-risk biologics (EMA 2017)',
           colors['purple'], 9)

create_box(ax, 9, 8, 4, 1.5,
           '**PK/PD Modeling**\n• Allometric PK scaling\n• PBPK models\n• Target engagement threshold\n• Efficacious exposure prediction',
           colors['purple_dark'], 9)

create_box(ax, 9, 6, 4, 1.5,
           '**Similar Compound Data**\n• Same class comparisons\n• Human MTD extrapolation\n• Mechanism-based scaling\n• Literature precedent',
           colors['cyan'], 9)

# Final decision node (centered in bottom panel)
create_box(ax, 7.5, 3.5, 4.5, 1.5,
           '**Final Starting Dose Selection**\n• Most conservative approach\n• Regulatory consultation\n• Risk-benefit assessment\n• Protocol justification',
           colors['red'], 10)

# Risk assessment diamond
create_diamond(ax, 12.5, 8, 2.5, 1.5, '**Risk\nAssessment**\n• Mechanism novelty\n• Target safety\n• Species relevance', colors['yellow'], 8)

# Regulatory guidance box
create_box(ax, 12.5, 10.5, 3.2, 1.5,
           '**Regulatory Guidance:**\n• FDA (2005): MRSD\n• EMA (2017): MABEL\n• ICH M3(R2): Nonclinical\n• ICH S9: Oncology drugs',
           colors['gray'], 8)

# Escalation strategy box
create_box(ax, 12.5, 4, 3.2, 1.5,
           '**Escalation Strategy:**\n• Modified Fibonacci\n• 100% → 67% → 50%\n• Sentinel dosing\n• Safety stopping rules\n• Exposure-based limits',
           colors['gray'], 8)

# Special considerations box
create_box(ax, 3, 2.5, 3.5, 1.2,
           '**Special Considerations:**\n• Oncology: 1/10 LD₁₀ or 1/6 STD₁₀\n• Biologics: MABEL mandatory\n• First-in-class: Extra caution\n• Pediatrics: Additional factors',
           colors['teal'], 8)

# Add arrows to show pathways
arrow_props = dict(arrowstyle='->', lw=2.5)

# Traditional pathway (vertical flow)
ax.annotate('', xy=(3, 8.2), xytext=(3, 9.8), arrowprops=dict(**arrow_props, color='blue'))
ax.annotate('', xy=(3, 6.2), xytext=(3, 7.8), arrowprops=dict(**arrow_props, color='blue'))
ax.annotate('', xy=(3, 4.2), xytext=(3, 5.8), arrowprops=dict(**arrow_props, color='blue'))

# Cross-pathway connection
ax.annotate('', xy=(7, 10), xytext=(4.8, 9), arrowprops=dict(**arrow_props, color='purple'))

# Mechanistic pathway (vertical flow)
ax.annotate('', xy=(9, 7.2), xytext=(9, 8.8), arrowprops=dict(**arrow_props, color='purple'))
ax.annotate('', xy=(9, 5.2), xytext=(9, 6.8), arrowprops=dict(**arrow_props, color='purple'))

# Risk assessment connections
ax.annotate('', xy=(11.2, 8.3), xytext=(10.8, 9.7), arrowprops=dict(arrowstyle='->', lw=2, color='gray'))
ax.annotate('', xy=(11.2, 7.7), xytext=(10.8, 8), arrowprops=dict(arrowstyle='->', lw=2, color='gray'))

# Convergence to final decision (centered)
ax.annotate('', xy=(5.5, 3.8), xytext=(3.8, 4.3), arrowprops=dict(**arrow_props, color='green'))
ax.annotate('', xy=(9.5, 3.8), xytext=(9, 5.2), arrowprops=dict(**arrow_props, color='purple'))

# Special considerations connection
ax.annotate('', xy=(4.5, 3.1), xytext=(5.5, 3.5), arrowprops=dict(arrowstyle='->', lw=2, color='teal', linestyle='--'))

plt.tight_layout()
plt.savefig('figure6_dose_translation.png', dpi=300, bbox_inches='tight', 
            facecolor='white', edgecolor='none')
plt.close()

print("Figure 6 (Dose Translation) generated successfully as figure6_dose_translation.png")
